import os
import chromadb
from sentence_transformers import SentenceTransformer
import fitz  # PyMuPDF
import sys
import hashlib
import json

# === Étape 1 : Extraire le texte depuis le PDF ===
def extraire_texte_depuis_pdf(chemin_pdf):
    if not os.path.isfile(chemin_pdf):
        print(f"ERREUR: Fichier PDF introuvable : {chemin_pdf}")
        print("Vérifiez le nom et l'emplacement du fichier.")
        sys.exit(1)
    doc = fitz.open(chemin_pdf)
    texte = ""
    for page in doc:
        texte += page.get_text()
    doc.close()
    return texte

# === Étape 2 : Diviser le texte en petits paragraphes (~100 mots) ===
def decouper_texte(texte, max_mots=100):
    mots = texte.split()
    chunks = []
    for i in range(0, len(mots), max_mots):
        chunk = " ".join(mots[i:i + max_mots])
        chunks.append(chunk)
    return chunks

# === Étape 3 (optionnelle mais recommandée) : Supprimer les anciens chunks d'un PDF ===
def supprimer_chunks_pdf(collection, nom_pdf):
    ids_a_supprimer = []
    results = collection.get()
    for doc_id in results["ids"]:
        if doc_id.startswith(nom_pdf):
            ids_a_supprimer.append(doc_id)
    if ids_a_supprimer:
        collection.delete(ids=ids_a_supprimer)
        print(f"{len(ids_a_supprimer)} anciens chunks supprimés pour : {nom_pdf}")
    else:
        print(f"INFO: Aucun ancien chunk à supprimer pour : {nom_pdf}")

# === Étape 4 : Calculer le hash SHA256 d'un PDF ===
def hash_pdf(chemin_pdf):
    sha256 = hashlib.sha256()
    with open(chemin_pdf, 'rb') as f:
        while True:
            data = f.read(65536)
            if not data:
                break
            sha256.update(data)
    return sha256.hexdigest()

# === Étape 5 : Charger/Sauvegarder le status des PDF ===
def charger_status(path="pdf_status.json"):
    if os.path.exists(path):
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def sauvegarder_status(status, path="pdf_status.json"):
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(status, f, indent=2, ensure_ascii=False)

# === Étape 6 : Injecter les nouveaux chunks dans la base ===
def creer_db(chemin_pdf, collection, model):
    nom_pdf = os.path.basename(chemin_pdf).replace(" ", "_").replace(".pdf", "")
    supprimer_chunks_pdf(collection, nom_pdf)
    texte = extraire_texte_depuis_pdf(chemin_pdf)
    chunks = decouper_texte(texte)
    for i, chunk in enumerate(chunks):
        embedding = model.encode(chunk).tolist()
        id_chunk = f"{nom_pdf}_chunk_{i}"
        collection.upsert(
            documents=[chunk],
            embeddings=[embedding],
            ids=[id_chunk]
        )
    print("SUCCES: Nouveau contenu injecté avec succès dans la base vectorielle.")

# === Lancer le script ===
if __name__ == "__main__":
    model = SentenceTransformer("all-MiniLM-L6-v2")
    client = chromadb.PersistentClient(path="./chroma_db")
    collection = client.get_or_create_collection(name="immobilier_suisse")

    dossier_pdfs = "PDFs"
    status_path = "pdf_status.json"
    status = charger_status(status_path)
    pdfs = [f for f in os.listdir(dossier_pdfs) if f.lower().endswith('.pdf')]
    maj = False

    for pdf in pdfs:
        chemin_pdf = os.path.join(dossier_pdfs, pdf)
        hash_actuel = hash_pdf(chemin_pdf)
        if pdf not in status or status[pdf] != hash_actuel:
            print(f"MAJ: Mise à jour de : {pdf}")
            creer_db(chemin_pdf, collection, model)
            status[pdf] = hash_actuel
            maj = True
        else:
            print(f"OK: Pas de modification détectée pour : {pdf}")

    if maj:
        sauvegarder_status(status, status_path)
        print("\nSUCCES: Fichier pdf_status.json mis à jour.")
    else:
        print("\nAucune mise à jour nécessaire. Tous les PDF sont déjà à jour dans la base vectorielle.")
