#!/usr/bin/env python3
"""
Script de test pour le service de scraping des taux hypothécaires
"""

from web_scraper import MortgageRateScraper
from scheduler import MortgageDataScheduler
import time

def test_scraping():
    """Test du scraping des taux hypothécaires"""
    print("=== TEST DU SCRAPING DES TAUX HYPOTHÉCAIRES ===")
    
    scraper = MortgageRateScraper()
    
    # Test 1: Scraping des taux actuels
    print("\n1. Test scraping des taux actuels...")
    rates = scraper.scrape_current_rates()
    
    if rates:
        print("SUCCES: Taux récupérés:")
        for rate in rates:
            print(f"  - {rate['duration']}: {rate['rate']}%")
    else:
        print("ECHEC: Aucun taux récupéré")
        return False
    
    # Test 2: Récupération depuis la base
    print("\n2. Test récupération depuis la base...")
    latest_rates = scraper.get_latest_rates()
    
    if latest_rates:
        print("SUCCES: Taux depuis la base:")
        for rate in latest_rates:
            print(f"  - {rate['duration']}: {rate['rate']}%")
    else:
        print("ECHEC: Aucun taux dans la base")
        return False
    
    # Test 3: Simulation hypothécaire
    print("\n3. Test simulation hypothécaire...")
    simulation = scraper.calculate_mortgage_simulation(500000, 20)
    
    if simulation:
        print("SUCCES: Simulation calculée:")
        print(f"  - Prêt: {simulation['loan_amount']:,.0f} CHF")
        print(f"  - Durée: {simulation['duration_years']} ans")
        print(f"  - Taux: {simulation['interest_rate']}%")
        print(f"  - Mensualité: {simulation['monthly_payment']:,.0f} CHF")
        print(f"  - Coût total: {simulation['total_cost']:,.0f} CHF")
    else:
        print("ECHEC: Impossible de calculer la simulation")
        return False
    
    # Test 4: Résumé des informations
    print("\n4. Test résumé des informations...")
    summary = scraper.get_mortgage_info_summary()
    print("SUCCES: Résumé généré:")
    print(summary)
    
    return True

def test_scheduler():
    """Test du planificateur"""
    print("\n=== TEST DU PLANIFICATEUR ===")
    
    scheduler = MortgageDataScheduler()
    
    # Test 1: Statut du système
    print("\n1. Test statut du système...")
    status = scheduler.get_status()
    
    print("SUCCES: Statut récupéré:")
    for key, value in status.items():
        print(f"  - {key}: {value}")
    
    # Test 2: Mise à jour manuelle
    print("\n2. Test mise à jour manuelle...")
    success = scheduler.manual_update()
    
    if success:
        print("SUCCES: Mise à jour manuelle réussie")
    else:
        print("ECHEC: Mise à jour manuelle échouée")
        return False
    
    # Test 3: Vérification de la mise à jour
    print("\n3. Test vérification de la mise à jour...")
    should_update = scheduler.should_update()
    print(f"Résultat: {'Mise à jour nécessaire' if should_update else 'Mise à jour non nécessaire'}")
    
    return True

def test_integration():
    """Test d'intégration avec le chatbot"""
    print("\n=== TEST D'INTÉGRATION AVEC LE CHATBOT ===")
    
    from chat_rag import ask_bot
    
    # Test 1: Question sur les taux
    print("\n1. Test question sur les taux hypothécaires...")
    print("Question: 'Quels sont les taux hypothécaires actuels en Suisse ?'")
    
    # Simuler une réponse (sans appeler l'API)
    print("Réponse simulée: Les taux actuels sont disponibles dans la base de données")
    
    # Test 2: Question de simulation
    print("\n2. Test question de simulation...")
    print("Question: 'Combien coûterait un prêt de 500'000 CHF sur 20 ans ?'")
    
    # Simuler une réponse
    print("Réponse simulée: Simulation calculée avec les taux actuels")
    
    return True

def main():
    """Fonction principale de test"""
    print("Démarrage des tests du système de scraping...")
    
    # Test 1: Scraping
    if not test_scraping():
        print("\n❌ Test de scraping échoué")
        return False
    
    # Test 2: Planificateur
    if not test_scheduler():
        print("\n❌ Test du planificateur échoué")
        return False
    
    # Test 3: Intégration
    if not test_integration():
        print("\n❌ Test d'intégration échoué")
        return False
    
    print("\n✅ TOUS LES TESTS RÉUSSIS !")
    print("Le système de scraping est prêt à être utilisé.")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Certains tests ont échoué. Vérifiez la configuration.")
        exit(1) 