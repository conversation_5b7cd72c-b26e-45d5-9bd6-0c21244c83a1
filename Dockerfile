# ✅ 1. Image de base
FROM python:3.10-slim

# ✅ 2. Dossier de travail
WORKDIR /app

# ✅ 3. Ajouter requirements.txt d’abord (pour profiter du cache)
COPY requirements.txt .

# ✅ 4. Installer les dépendances système (stables)
RUN apt-get update && apt-get install -y \
    build-essential \
    libgl1-mesa-glx \
    wget \
    gnupg \
    git \
    libffi-dev \
    libcairo2 \
    pkg-config \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# ✅ 5. Installer Chrome, ChromeDriver, Selenium
RUN apt-get update && \
    apt-get install -y wget gnupg2 && \
    wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && \
    apt-get install -y google-chrome-stable && \
    pip install --no-cache-dir selenium chromedriver-autoinstaller && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# ✅ 6. Installer les dépendances Python (groupées pour le cache pip)
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir --extra-index-url https://download.pytorch.org/whl/cpu -r requirements.txt

# ✅ 7. Pré-télécharger le modèle ST
RUN python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('all-MiniLM-L6-v2')"

# ✅ 8. Copier les fichiers de l’app (à la fin pour éviter rebuild trop fréquent)
COPY . .

# ✅ 9. Exposer le port
EXPOSE 8090

# ✅ 10. Commande de lancement
CMD ["python", "-u", "main.py"]