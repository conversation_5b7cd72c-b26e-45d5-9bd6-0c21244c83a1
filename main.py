#!/usr/bin/env python3
"""
Script principal pour l'assistant immobilier suisse
Orchestre la création de la base de données, le test des liens web et le lancement du chatbot
Architecture microservices maintenue
"""

import subprocess
import sys
import os
import time

from web_scraper import SELENIUM_AVAILABLE, SwissMortgageScraper

# --- PATCH: Forcer la réindexation si demandé (Docker)
if os.environ.get("FORCE_REINDEX") == "1":
    import shutil
    if os.path.exists("pdf_status.json"):
        print("[INFO] Suppression de pdf_status.json pour forcer la réindexation...")
        os.remove("pdf_status.json")
    if os.path.exists("chroma_db"):
        print("[INFO] Nettoyage du dossier chroma_db/ pour forcer la réindexation...")
        for filename in os.listdir("chroma_db"):
            file_path = os.path.join("chroma_db", filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                print(f"  [WARNING] Impossible de supprimer {file_path}: {e}")

def run_command(command, description):
    """Exécute une commande et affiche le résultat"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} terminé avec succès")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de {description}:")
        print(f"   Code d'erreur: {e.returncode}")
        if e.stdout:
            print(f"   Sortie: {e.stdout}")
        if e.stderr:
            print(f"   Erreur: {e.stderr}")
        return False

def check_pdfs_directory():
    """Vérifie que le dossier PDFs existe et contient des fichiers"""
    pdfs_dir = "PDFs"
    if not os.path.exists(pdfs_dir):
        print(f"ERREUR: Dossier {pdfs_dir} introuvable")
        print(f"   Création du dossier {pdfs_dir}...")
        os.makedirs(pdfs_dir, exist_ok=True)
        print(f"SUCCES: Dossier {pdfs_dir} créé")
        print(f"   Veuillez ajouter vos PDFs dans le dossier {pdfs_dir}/")
        return False
    
    pdf_files = [f for f in os.listdir(pdfs_dir) if f.lower().endswith('.pdf')]
    if not pdf_files:
        print(f"ATTENTION: Aucun PDF trouvé dans le dossier {pdfs_dir}/")
        print(f"   Ajoutez vos PDFs dans le dossier {pdfs_dir}/ pour que le bot puisse répondre")
        return False
    
    print(f"SUCCES: {len(pdf_files)} PDF(s) trouvé(s) dans {pdfs_dir}/")
    return True

def test_web_links():
    """Version améliorée du test des liens web"""
    print("\n🌐 ÉTAPE 1.5: Test des liens web (SwissMortgageScraper)")
    print("-" * 40)
    
    try:
        print("🔄 Initialisation du scraper hypothécaire suisse...")
        scraper = SwissMortgageScraper(headless=True)
        
        # 1. Test scraping des taux avec fallback intelligent
        print("  🔍 Scraping des taux hypothécaires...")
        rates = []
        
        # Essai d'abord sans Selenium
        rates = scraper.scrape_moneypark_rates(use_selenium=False)
        
        # Si échec ou données peu fiables (confidence < 0.5), essayer avec Selenium
        if not rates or any(r.get('confidence_score', 0) < 0.5 for r in rates):
            print("  → Tentative avec Selenium (mode navigateur)...")
            rates = scraper.scrape_moneypark_rates(use_selenium=True)

        if rates:
            print(f"  ✅ {len(rates)} taux récupérés")
            for rate in sorted(rates, key=lambda x: x['duration']):
                conf = rate.get('confidence_score', 0)
                print(f"    - {rate['duration']}: {rate['rate']}% (confiance: {conf:.2f})")
        else:
            print("  ⚠️  Aucun taux valide trouvé, utilisation des valeurs par défaut")
            rates = scraper.get_latest_rates()
            for rate in rates:
                print(f"    - {rate['duration']}: {rate['rate']}% (source: {rate['method']})")

        # 2. Test simulation hypothécaire avec fallback
        print("\n  🧮 Simulation hypothécaire...")
        loan_amount = 500000
        duration = 20
        
        # Essai d'abord avec calculateur en ligne si disponible
        if SELENIUM_AVAILABLE:
            print("  → Essai avec calculateur en ligne...")
            simulation = scraper.calculate_mortgage(loan_amount, duration, use_calculator=True)
        else:
            simulation = scraper.calculate_mortgage(loan_amount, duration, use_calculator=False)
        
        print(f"    - Montant: {loan_amount:,} CHF")
        print(f"    - Durée: {duration} ans")
        print(f"    - Mensualité: {simulation['monthly_payment']:,.2f} CHF")
        print(f"    - Méthode: {simulation['calculation_method']}")
        print(f"    - Taux utilisé: {simulation['interest_rate']}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        return False


def main():
    """Fonction principale"""
    print("ASSISTANT IMMOBILIER SUISSE - DEMARRAGE")
    print("=" * 50)
    
    # Vérification des PDFs
    if not check_pdfs_directory():
        print("\nERREUR: Impossible de continuer sans PDFs")
        print("   Ajoutez vos PDFs dans le dossier PDFs/ et relancez le script")
        sys.exit(1)
    
    # Étape 1: Création de la base de données
    print("\n📚 ÉTAPE 1: Préparation de la base de données")
    print("-" * 40)
    
    create_db_success = run_command(
        "python create_db.py",
        "Création de la base de données vectorielle"
    )
    
    if not create_db_success:
        print("\nERREUR: Échec de la création de la base de données")
        print("   Vérifiez que tous les fichiers sont présents et que les PDFs sont valides")
        sys.exit(1)
    
    # Étape 1.5: Test des liens web
    web_test_success = test_web_links()
    
    # Étape 2: Lancement du chatbot
    print("\n🤖 ÉTAPE 2: Lancement du chatbot")
    print("-" * 40)
    
    print("SUCCES: Base de données prête")
    if web_test_success:
        print("SUCCES: Liens web testés et fonctionnels")
    else:
        print("ATTENTION: Liens web non disponibles, utilisation des données locales")
    
    print("🚀 Lancement du chatbot...")
    print("\n" + "=" * 50)
    print("🤖 Assistant immobilier suisse prêt !")
    print("Posez vos questions sur l'immobilier en Suisse")
    print("Tapez 'exit' pour quitter")
    print("=" * 50)
    
    # Lancement du chatbot web
    print("\n🌐 Lancement de l'interface web...")
    try:
        subprocess.run([sys.executable, "app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"\nERREUR: Erreur lors du lancement du chatbot web: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Arrêt du chatbot")
    except EOFError:
        print("\n\n👋 Fin de session")

if __name__ == "__main__":
    main()