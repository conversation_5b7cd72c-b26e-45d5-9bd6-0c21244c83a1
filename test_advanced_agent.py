#!/usr/bin/env python3
"""
Script de test pour l'agent IA avancé
Teste le scraping intelligent et l'interaction avec les formulaires
"""

import sys
import time
from advanced_web_agent import AdvancedMortgageAgent

def test_advanced_agent():
    """Test complet de l'agent IA avancé"""
    print("=" * 70)
    print("TEST DE L'AGENT IA AVANCÉ - SCRAPING INTELLIGENT")
    print("=" * 70)
    
    # Initialiser l'agent (headless=False pour voir ce qui se passe)
    print("\n1. Initialisation de l'agent IA avancé...")
    agent = AdvancedMortgageAgent(headless=False, db_path="test_advanced_mortgage.db")
    print("✓ Agent initialisé avec succès")
    
    try:
        # Test 1: Scraping intelligent des taux
        print("\n2. Test scraping intelligent des taux hypothécaires...")
        print("URL: https://moneypark.ch/fr/hypotheque/taux-interet-hypothecaires-actuels/")
        
        start_time = time.time()
        rates = agent.scrape_moneypark_rates()
        end_time = time.time()
        
        if rates:
            print(f"✓ Scraping intelligent réussi en {end_time - start_time:.2f} secondes")
            print(f"✓ {len(rates)} taux récupérés:")
            
            for rate in rates:
                confidence = rate.get('confidence_score', 0)
                source = rate.get('source_url', 'unknown')
                print(f"  - {rate['duration']}: {rate['rate']}% (confiance: {confidence:.2f}, source: {source})")
        else:
            print("✗ Échec du scraping intelligent")
            return False
        
        # Test 2: Utilisation du calculateur MoneyPark
        print("\n3. Test utilisation du calculateur MoneyPark...")
        print("URL: https://moneypark.ch/fr/hypotheque/calculateur/")
        
        test_cases = [
            (500000, 20, "Prêt moyen"),
            (1000000, 25, "Prêt important"),
            (250000, 15, "Petit prêt")
        ]
        
        for loan_amount, duration, description in test_cases:
            print(f"\n  Test {description}:")
            print(f"  - Montant: {loan_amount:,} CHF")
            print(f"  - Durée: {duration} ans")
            
            simulation = agent.use_moneypark_calculator(loan_amount, duration)
            
            if simulation:
                print(f"  ✓ Simulation réussie:")
                print(f"    * Mensualité: {simulation.get('monthly_payment', 'N/A'):,.0f} CHF")
                print(f"    * Méthode: {simulation.get('calculation_method', 'N/A')}")
                if simulation.get('total_cost'):
                    print(f"    * Coût total: {simulation['total_cost']:,.0f} CHF")
                if simulation.get('interest_rate'):
                    print(f"    * Taux: {simulation['interest_rate']}%")
            else:
                print(f"  ✗ Échec de la simulation")
        
        # Test 3: Statistiques de scraping
        print("\n4. Statistiques de scraping...")
        stats = agent.get_scraping_statistics()
        
        print(f"✓ Statistiques récupérées:")
        print(f"  - Total tentatives: {stats['total_attempts']}")
        print(f"  - Tentatives réussies: {stats['successful_attempts']}")
        print(f"  - Taux de succès: {stats['success_rate']:.1f}%")
        print(f"  - Temps de réponse moyen: {stats['avg_response_time']:.2f}s")
        
        # Test 4: Récupération des taux depuis la base
        print("\n5. Test récupération depuis la base de données...")
        latest_rates = agent.get_latest_rates()
        
        if latest_rates:
            print(f"✓ {len(latest_rates)} taux récupérés depuis la DB:")
            for rate in latest_rates:
                print(f"  - {rate['duration']}: {rate['rate']}% (confiance: {rate['confidence_score']:.2f})")
        else:
            print("✗ Aucun taux trouvé en base de données")
        
        # Test 5: Test de robustesse
        print("\n6. Test de robustesse...")
        
        # Test avec des URLs qui pourraient échouer
        test_urls = [
            "https://moneypark.ch/fr/hypotheque/taux-interet-hypothecaires-actuels/",
            "https://www.comparis.ch/hypotheque/taux",
            "https://www.credit-suisse.ch/hypotheque"
        ]
        
        for url in test_urls:
            print(f"\n  Test de robustesse pour {url}...")
            try:
                agent.driver.get(url)
                time.sleep(2)
                
                if "moneypark" in url.lower():
                    # Essayer d'extraire des données
                    rates = agent.extract_rates_from_text()
                    print(f"    ✓ {len(rates)} taux extraits du texte")
                else:
                    print(f"    ⚠ Page accessible mais pas analysée")
                    
            except Exception as e:
                print(f"    ✗ Erreur: {e}")
        
        print("\n" + "=" * 70)
        print("✅ TOUS LES TESTS DE L'AGENT IA AVANCÉ RÉUSSIS!")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        return False
        
    finally:
        # Fermer l'agent
        print("\n7. Fermeture de l'agent...")
        agent.close()
        print("✓ Agent fermé avec succès")

def test_comparison_with_old_system():
    """Compare l'agent avancé avec l'ancien système"""
    print("\n" + "=" * 70)
    print("COMPARAISON AVEC L'ANCIEN SYSTÈME")
    print("=" * 70)
    
    from web_scraper import MortgageRateScraper
    
    print("\n1. Test ancien système (web_scraper.py)...")
    old_scraper = MortgageRateScraper("test_old_system.db")
    
    start_time = time.time()
    old_rates = old_scraper.scrape_current_rates()
    old_time = time.time() - start_time
    
    print(f"✓ Ancien système: {len(old_rates)} taux en {old_time:.2f}s")
    
    print("\n2. Test nouveau système (advanced_web_agent.py)...")
    new_agent = AdvancedMortgageAgent(headless=True, db_path="test_new_system.db")
    
    try:
        start_time = time.time()
        new_rates = new_agent.scrape_moneypark_rates()
        new_time = time.time() - start_time
        
        print(f"✓ Nouveau système: {len(new_rates)} taux en {new_time:.2f}s")
        
        print("\n3. Comparaison des résultats:")
        print(f"  - Ancien système: {len(old_rates)} taux, {old_time:.2f}s")
        print(f"  - Nouveau système: {len(new_rates)} taux, {new_time:.2f}s")
        print(f"  - Amélioration: {((len(new_rates) - len(old_rates)) / max(len(old_rates), 1) * 100):.1f}% plus de taux")
        
        if new_rates and old_rates:
            # Comparer la qualité des données
            old_confidence = sum(1 for r in old_rates if 'confidence_score' not in r)
            new_confidence = sum(r.get('confidence_score', 0) for r in new_rates)
            
            print(f"  - Qualité des données: {new_confidence:.2f} vs {old_confidence}")
        
    finally:
        new_agent.close()

def main():
    """Fonction principale de test"""
    print("Démarrage des tests de l'agent IA avancé...")
    
    # Test principal
    success = test_advanced_agent()
    
    if success:
        # Test de comparaison
        test_comparison_with_old_system()
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("L'agent IA avancé est prêt pour la production.")
        print("\n📋 RÉCAPITULATIF DES AMÉLIORATIONS:")
        print("✅ Scraping intelligent avec Selenium")
        print("✅ Interaction avec les formulaires")
        print("✅ Gestion des anti-bot")
        print("✅ Scores de confiance")
        print("✅ Statistiques détaillées")
        print("✅ Fallback intelligent")
        print("✅ Base de données avancée")
        
    else:
        print("\n❌ Certains tests ont échoué.")
        sys.exit(1)

if __name__ == "__main__":
    main() 