/* Effets visuels avancés inspirés de Gemini */

/* Variables CSS pour les animations */
:root {
  --gemini-primary: #4285f4;
  --gemini-secondary: #34a853;
  --gemini-accent: #ea4335;
  --gemini-warning: #fbbc04;
  --animation-duration: 0.3s;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --shadow-elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

/* Animation de pulsation pour l'indicateur de frappe */
@keyframes geminiPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* Animation de vague pour le texte qui apparaît */
@keyframes textWave {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Animation de brillance pour les boutons */
@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Animation de respiration pour les éléments interactifs */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Effet de particules flottantes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Classes utilitaires pour les animations */
.animate-fade-in {
  animation: fadeInText var(--animation-duration) var(--animation-easing) forwards;
}

.animate-slide-up {
  animation: slideInMessage var(--animation-duration) var(--animation-easing) forwards;
}

.animate-pulse {
  animation: geminiPulse 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-breathe {
  animation: breathe 3s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Effets de survol avancés */
.hover-lift {
  transition: all var(--animation-duration) var(--animation-easing);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevation-2);
}

.hover-glow {
  transition: all var(--animation-duration) var(--animation-easing);
  position: relative;
  overflow: hidden;
}

.hover-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.hover-glow:hover::before {
  left: 100%;
}

/* Styles pour les messages avec effet Gemini */
.message-gemini {
  position: relative;
  overflow: hidden;
}

.message-gemini::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.message-gemini:hover::after {
  transform: translateX(100%);
}

/* Indicateur de frappe amélioré */
.typing-enhanced {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
}

.typing-enhanced .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--gemini-primary);
  animation: geminiTyping 1.4s ease-in-out infinite both;
}

.typing-enhanced .dot:nth-child(1) { animation-delay: 0s; }
.typing-enhanced .dot:nth-child(2) { animation-delay: 0.2s; }
.typing-enhanced .dot:nth-child(3) { animation-delay: 0.4s; }

/* Bouton avec effet Gemini */
.btn-gemini {
  position: relative;
  background: linear-gradient(135deg, var(--gemini-primary) 0%, var(--gemini-secondary) 100%);
  border: none;
  border-radius: 24px;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  cursor: pointer;
  overflow: hidden;
  transition: all var(--animation-duration) var(--animation-easing);
  box-shadow: var(--shadow-elevation-1);
}

.btn-gemini::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn-gemini:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevation-2);
}

.btn-gemini:hover::before {
  left: 100%;
}

.btn-gemini:active {
  transform: translateY(0);
  box-shadow: var(--shadow-elevation-1);
}

/* Input avec effet focus Gemini */
.input-gemini {
  border: 2px solid transparent;
  border-radius: 24px;
  padding: 12px 20px;
  font-size: 16px;
  transition: all var(--animation-duration) var(--animation-easing);
  background: rgba(255,255,255,0.9);
  backdrop-filter: blur(10px);
}

.input-gemini:focus {
  outline: none;
  border-color: var(--gemini-primary);
  box-shadow: 0 0 0 4px rgba(66, 133, 244, 0.1);
  transform: translateY(-1px);
}

/* Effet de particules de fond */
.particles-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--gemini-primary);
  border-radius: 50%;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(odd) {
  animation-delay: -2s;
  background: var(--gemini-secondary);
}

.particle:nth-child(3n) {
  animation-delay: -4s;
  background: var(--gemini-accent);
}

/* Responsive design pour les animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mode sombre pour les effets */
@media (prefers-color-scheme: dark) {
  :root {
    --shadow-elevation-1: 0 1px 3px rgba(255,255,255,0.12), 0 1px 2px rgba(255,255,255,0.24);
    --shadow-elevation-2: 0 3px 6px rgba(255,255,255,0.16), 0 3px 6px rgba(255,255,255,0.23);
    --shadow-elevation-3: 0 10px 20px rgba(255,255,255,0.19), 0 6px 6px rgba(255,255,255,0.23);
  }
  
  .input-gemini {
    background: rgba(0,0,0,0.9);
    color: white;
  }
}
