#!/usr/bin/env python3
"""
Playground de test pour comparer les modèles LLM
Teste Mistral, Gemini 2.5 Pro et Gemini 1.5 Pro Latest
"""

import time
import os
from datetime import datetime
from unified_llm import ask_llm
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration des modèles à tester
MODELS_TO_TEST = {
    "mistral": "mistral/mistral-tiny",
    "gemini-2.5-pro": "gemini/gemini-2.5-pro", 
    "gemini-1.5-pro-latest": "gemini/gemini-1.5-pro-latest"
}

# Questions de test pour l'immobilier suisse
TEST_QUESTIONS = [
    {
        "category": "Taux hypothécaires",
        "question": "Quels sont les facteurs qui influencent les taux hypothécaires en Suisse ?",
        "expected_keywords": ["banque nationale", "inflation", "marché", "risque"]
    },
    {
        "category": "Simulation prêt",
        "question": "Comment calculer la mensualité d'un prêt hypothécaire de 800'000 CHF sur 25 ans ?",
        "expected_keywords": ["mensualité", "taux", "amortissement", "intérêts"]
    },
    {
        "category": "Réglementation",
        "question": "Quelles sont les exigences de fonds propres pour un achat immobilier en Suisse ?",
        "expected_keywords": ["20%", "fonds propres", "LTV", "financement"]
    },
    {
        "category": "Types d'hypothèques",
        "question": "Quelle est la différence entre une hypothèque fixe et variable ?",
        "expected_keywords": ["fixe", "variable", "taux", "risque", "durée"]
    }
]

def test_model_response(model_name, model_id, question_data):
    """Teste un modèle avec une question spécifique"""
    print(f"\n🤖 Test {model_name}")
    print(f"📝 Question: {question_data['question']}")
    
    try:
        start_time = time.time()
        response = ask_llm(question_data['question'], model_name=model_id)
        end_time = time.time()
        
        response_time = end_time - start_time
        word_count = len(response.split())
        
        # Vérifier la présence de mots-clés attendus
        keywords_found = []
        for keyword in question_data['expected_keywords']:
            if keyword.lower() in response.lower():
                keywords_found.append(keyword)
        
        keyword_score = len(keywords_found) / len(question_data['expected_keywords']) * 100
        
        print(f"⏱️  Temps de réponse: {response_time:.2f}s")
        print(f"📊 Nombre de mots: {word_count}")
        print(f"🎯 Score mots-clés: {keyword_score:.1f}% ({len(keywords_found)}/{len(question_data['expected_keywords'])})")
        print(f"🔍 Mots-clés trouvés: {', '.join(keywords_found)}")
        print(f"📄 Réponse (100 premiers caractères): {response[:100]}...")
        
        return {
            "success": True,
            "response_time": response_time,
            "word_count": word_count,
            "keyword_score": keyword_score,
            "keywords_found": keywords_found,
            "response": response
        }
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "response_time": 0,
            "word_count": 0,
            "keyword_score": 0,
            "keywords_found": [],
            "response": ""
        }

def run_model_comparison():
    """Lance la comparaison complète des modèles"""
    print("🚀 PLAYGROUND DE TEST DES MODÈLES LLM")
    print("=" * 80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 Modèles testés: {', '.join(MODELS_TO_TEST.keys())}")
    print(f"❓ Questions de test: {len(TEST_QUESTIONS)}")
    
    # Stockage des résultats
    results = {}
    
    for question_data in TEST_QUESTIONS:
        print(f"\n" + "=" * 80)
        print(f"📋 CATÉGORIE: {question_data['category']}")
        print("=" * 80)
        
        question_results = {}
        
        for model_name, model_id in MODELS_TO_TEST.items():
            result = test_model_response(model_name, model_id, question_data)
            question_results[model_name] = result
            
            # Pause entre les modèles pour éviter les rate limits
            time.sleep(1)
        
        results[question_data['category']] = question_results
    
    return results

def display_summary(results):
    """Affiche un résumé comparatif des résultats"""
    print(f"\n" + "=" * 80)
    print("📊 RÉSUMÉ COMPARATIF")
    print("=" * 80)
    
    # Calcul des moyennes par modèle
    model_stats = {}
    
    for model_name in MODELS_TO_TEST.keys():
        total_time = 0
        total_words = 0
        total_keyword_score = 0
        success_count = 0
        total_tests = 0
        
        for category, category_results in results.items():
            if model_name in category_results:
                result = category_results[model_name]
                total_tests += 1
                
                if result['success']:
                    success_count += 1
                    total_time += result['response_time']
                    total_words += result['word_count']
                    total_keyword_score += result['keyword_score']
        
        if success_count > 0:
            model_stats[model_name] = {
                "success_rate": (success_count / total_tests) * 100,
                "avg_response_time": total_time / success_count,
                "avg_word_count": total_words / success_count,
                "avg_keyword_score": total_keyword_score / success_count
            }
        else:
            model_stats[model_name] = {
                "success_rate": 0,
                "avg_response_time": 0,
                "avg_word_count": 0,
                "avg_keyword_score": 0
            }
    
    # Affichage du tableau comparatif
    print(f"\n{'Modèle':<20} {'Succès':<8} {'Temps':<8} {'Mots':<8} {'Score':<8}")
    print("-" * 60)
    
    for model_name, stats in model_stats.items():
        print(f"{model_name:<20} "
              f"{stats['success_rate']:.1f}%{'':<3} "
              f"{stats['avg_response_time']:.2f}s{'':<3} "
              f"{stats['avg_word_count']:.0f}{'':<4} "
              f"{stats['avg_keyword_score']:.1f}%")
    
    # Recommandations
    print(f"\n🏆 RECOMMANDATIONS:")
    
    # Meilleur temps de réponse
    fastest_model = min(model_stats.items(), 
                       key=lambda x: x[1]['avg_response_time'] if x[1]['success_rate'] > 0 else float('inf'))
    print(f"⚡ Plus rapide: {fastest_model[0]} ({fastest_model[1]['avg_response_time']:.2f}s)")
    
    # Meilleur score de pertinence
    best_relevance = max(model_stats.items(), 
                        key=lambda x: x[1]['avg_keyword_score'])
    print(f"🎯 Plus pertinent: {best_relevance[0]} ({best_relevance[1]['avg_keyword_score']:.1f}%)")
    
    # Réponses les plus détaillées
    most_detailed = max(model_stats.items(), 
                       key=lambda x: x[1]['avg_word_count'])
    print(f"📝 Plus détaillé: {most_detailed[0]} ({most_detailed[1]['avg_word_count']:.0f} mots)")

def main():
    """Fonction principale"""
    print("🧪 DÉMARRAGE DU PLAYGROUND DE TEST")
    
    # Vérifier la configuration
    if not os.getenv("LITELLM_MODEL"):
        print("⚠️  Variable LITELLM_MODEL non définie dans .env")
    
    # Lancer les tests
    results = run_model_comparison()
    
    # Afficher le résumé
    display_summary(results)
    
    print(f"\n✅ Tests terminés à {datetime.now().strftime('%H:%M:%S')}")
    print("\n💡 Pour changer de modèle par défaut, modifiez LITELLM_MODEL dans votre fichier .env")

if __name__ == "__main__":
    main()
