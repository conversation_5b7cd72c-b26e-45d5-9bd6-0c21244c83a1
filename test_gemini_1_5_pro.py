#!/usr/bin/env python3
"""
Test simple pour vérifier que Gemini 1.5 Pro Latest fonctionne
"""

from unified_llm import ask_llm
import time

def test_gemini_1_5_pro():
    """Test simple de Gemini 1.5 Pro Latest"""
    print("🧪 Test de Gemini 1.5 Pro Latest")
    print("=" * 50)
    
    model_name = "gemini/gemini-1.5-pro-latest"
    question = "Explique brièvement les taux hypothécaires en Suisse."
    
    try:
        print(f"🤖 Modèle: {model_name}")
        print(f"❓ Question: {question}")
        print("\n⏳ Envoi de la requête...")
        
        start_time = time.time()
        response = ask_llm(question, model_name=model_name)
        end_time = time.time()
        
        print(f"✅ Réponse reçue en {end_time - start_time:.2f} secondes")
        print(f"📝 Longueur: {len(response)} caractères")
        print("\n📄 Réponse:")
        print("-" * 50)
        print(response)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False

def compare_all_models():
    """Compare rapidement les 3 modèles"""
    print("\n🔄 Comparaison rapide des 3 modèles")
    print("=" * 50)
    
    models = {
        "Mistral Tiny": "mistral/mistral-tiny",
        "Gemini 2.5 Pro": "gemini/gemini-2.5-pro", 
        "Gemini 1.5 Pro Latest": "gemini/gemini-1.5-pro-latest"
    }
    
    question = "Qu'est-ce qu'une hypothèque fixe ?"
    
    for model_display, model_id in models.items():
        print(f"\n🤖 {model_display}:")
        try:
            start_time = time.time()
            response = ask_llm(question, model_name=model_id)
            end_time = time.time()
            
            print(f"  ⏱️  {end_time - start_time:.2f}s")
            print(f"  📝 {len(response)} caractères")
            print(f"  📄 {response[:100]}...")
            
        except Exception as e:
            print(f"  ❌ Erreur: {str(e)}")
        
        # Pause pour éviter les rate limits
        time.sleep(1)

if __name__ == "__main__":
    print("🚀 Test des modèles LLM")
    
    # Test spécifique de Gemini 1.5 Pro Latest
    success = test_gemini_1_5_pro()
    
    if success:
        # Comparaison rapide des 3 modèles
        compare_all_models()
        
        print("\n✅ Tous les tests sont terminés !")
        print("💡 Vous pouvez maintenant utiliser playground_models.py pour des tests plus détaillés")
    else:
        print("\n❌ Le test de base a échoué")
        print("🔧 Vérifiez votre configuration .env et vos clés API")
