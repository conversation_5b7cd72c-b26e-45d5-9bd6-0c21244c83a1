import os
import fitz  # PyMuPDF

def extraire_texte_depuis_pdf(chemin_pdf):
    doc = fitz.open(chemin_pdf)
    texte_complet = ""

    for page in doc:
        texte_complet += page.get_text()
    
    doc.close()
    return texte_complet


# Test : Affiche les 1000 premiers caractères du texte extrait
if __name__ == "__main__":
    # 🔁 Mets ici le chemin correct vers ton fichier PDF
    chemin_pdf = os.path.join("PDFs", "Property_Market_Report_Switzerland_Q2_2025_FR(1).pdf")
    
    texte = extraire_texte_depuis_pdf(chemin_pdf)
    
    print(texte[:1000])  # affiche juste le début pour vérifier
