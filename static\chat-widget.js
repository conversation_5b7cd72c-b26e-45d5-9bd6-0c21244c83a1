class ChatWidget extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
    this.chatHistory = [];
  }

  connectedCallback() {
    this.render();
    this.loadHistory();
    this.renderHistory();
    this.addListeners();
    this.observeDarkMode();
    // Restore last used model
    setTimeout(() => {
      try {
        const modelSelect = window.parent.document.getElementById('model-select');
        if (modelSelect) {
          const last = localStorage.getItem('selectedModel');
          if (last) modelSelect.value = last;
        }
      } catch {}
    }, 100);
  }

  getTheme() {
    // Suivre la classe du body pour le dark mode global
    return document.body.classList.contains('dark-theme') ? 'dark' : 'light';
  }

  observeDarkMode() {
    // Observer les changements de classe sur le body pour le dark mode
    const observer = new MutationObserver(() => {
      this.setTheme(this.getTheme());
    });
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
    this.setTheme(this.getTheme());
  }

  setTheme(theme) {
    const container = this.shadowRoot.querySelector('.container');
    container.classList.remove('light-theme', 'dark-theme');
    container.classList.add(`${theme}-theme`);
  }

  render() {
    this.shadowRoot.innerHTML = `
      <style>
        .light-theme {
          --bg-primary: #ffffff;
          --bg-secondary: #f0f3f4;
          --text-primary: #111518;
          --text-secondary: #637c88;
          --border-color: #f0f3f4;
          --user-message: #19a1e5;
          --bot-message: #f0f3f4;
          --input-bg: #f0f3f4;
          --button-bg: #19a1e5;
        }
        .dark-theme {
          --bg-primary: #111618;
          --bg-secondary: #283339;
          --text-primary: #ffffff;
          --text-secondary: #9db0b9;
          --border-color: #283339;
          --user-message: #1193d4;
          --bot-message: #283339;
          --input-bg: #283339;
          --button-bg: #1193d4;
        }
        .theme-transition { transition: all 0.3s ease; }
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          background: var(--bg-primary);
          border-radius: 18px;
          box-shadow: 0 2px 12px 0 #0001;
          padding-bottom: 32px;
        }
        .main-chat-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          padding-top: 100px;
          padding-bottom: 80px;
        }
        .chatbox { width: 100%; max-width: 896px; display: flex; flex-direction: column; flex: 1; }
        .welcome {
          text-align: center;
          color: var(--text-primary);
          margin-bottom: 12px;
          font-size: 1.8rem;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0,0,0,0.1);
          padding: 0 16px;
        }
        .subtitle { text-align: center; color: var(--text-primary); margin-bottom: 16px; }
        .messages { flex: 1; padding: 0 8px; }
        .msg-row { display: flex; gap: 12px; margin-bottom: 0.5rem; align-items: center; }
        .msg-row.user { flex-direction: row-reverse; }
        .avatar { width: 40px; height: 40px; border-radius: 50%; background-size: cover; background-position: center; flex-shrink: 0; }
        .bubble { border-radius: 16px; padding: 12px 18px; max-width: 80%; font-size: 1rem; line-height: 1.6; word-break: break-word; box-sizing: border-box; }
        .bubble.user {
          background: var(--user-message);
          color: #fff;
          align-self: flex-end;
          padding: 6px 12px;
          border-radius: 16px;
          min-width: 0;
          width: fit-content;
          display: inline-block;
          line-height: 1.4;
          font-size: 0.95rem;
        }
        .bubble.bot { background: var(--bot-message); color: var(--text-primary); align-self: flex-start; }
        .bubble :is(h1,h2,h3,h4,h5,h6) { margin: 0.5em 0 0.2em 0; }
        .bubble ul, .bubble ol { margin: 0.5em 0 0.5em 1.2em; }
        .bubble pre { background: #eaeaea; border-radius: 8px; padding: 8px; overflow-x: auto; }
        .bubble code { background: #eaeaea; border-radius: 4px; padding: 2px 4px; }
        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            margin: 0 1px;
            animation: blink 1.4s infinite both;
        }
        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }
        @keyframes blink {
            0% { opacity: 0.2; }
            20% { opacity: 1; }
            100% { opacity: 0.2; }
        }
        .input-bar {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          width: 100%;
          background: var(--bg-primary);
          border-top: 1px solid var(--border-color);
          z-index: 50;
          padding: 12px 0;
          box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        .input-inner {
          max-width: 896px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          padding: 0 16px;
          gap: 12px;
        }
        .input-box {
          flex: 1;
          border-radius: 24px;
          background: var(--input-bg);
          border: 2px solid transparent;
          padding: 0 20px;
          height: 48px;
          font-size: 1rem;
          color: var(--text-primary);
          transition: all 0.2s ease;
          min-width: 0;
        }
        .input-box:focus {
          outline: none;
          border-color: var(--button-bg);
          box-shadow: 0 0 0 3px rgba(25, 161, 229, 0.1);
        }
        .input-box::placeholder {
          color: var(--text-secondary);
        }
        .send-btn {
          background: var(--button-bg);
          color: #fff;
          border: none;
          border-radius: 24px;
          padding: 0 24px;
          height: 48px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          flex-shrink: 0;
          min-width: 80px;
        }
        .send-btn:hover {
          background: #1193d4;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(25, 161, 229, 0.3);
        }
        .send-btn:active {
          transform: translateY(0);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .main-chat-area {
            padding-top: 80px;
            padding-bottom: 100px;
            padding-left: 8px;
            padding-right: 8px;
          }
          .welcome {
            font-size: 1.5rem;
            padding: 0 12px;
          }
          .subtitle {
            font-size: 0.9rem;
            padding: 0 12px;
          }
          .input-inner {
            padding: 0 12px;
            gap: 8px;
          }
          .input-box {
            height: 44px;
            font-size: 0.95rem;
            padding: 0 16px;
          }
          .send-btn {
            height: 44px;
            padding: 0 20px;
            font-size: 0.9rem;
            min-width: 70px;
          }
        }

        @media (max-width: 480px) {
          .main-chat-area {
            padding-top: 70px;
            padding-bottom: 90px;
            padding-left: 4px;
            padding-right: 4px;
          }
          .welcome {
            font-size: 1.3rem;
            padding: 0 8px;
          }
          .subtitle {
            font-size: 0.85rem;
            padding: 0 8px;
          }
          .input-inner {
            padding: 0 8px;
            gap: 6px;
          }
          .input-box {
            height: 40px;
            font-size: 0.9rem;
            padding: 0 14px;
            border-radius: 20px;
          }
          .send-btn {
            height: 40px;
            padding: 0 16px;
            font-size: 0.85rem;
            min-width: 60px;
            border-radius: 20px;
          }
        }
      </style>
      <div class="container theme-transition">
        <div class="main-chat-area">
          <div class="chatbox">
            <h2 class="welcome">Bienvenue sur Swiss Estate AI</h2>
            <p class="subtitle">Posez-moi toutes vos questions sur le marché immobilier suisse. Je peux vous aider à trouver des propriétés, à comprendre les tendances du marché, et bien plus encore.</p>
            <div class="messages"></div>
          </div>
        </div>
        <div class="input-bar">
          <div class="input-inner">
            <input class="input-box" type="text" placeholder="Tapez votre question ici..." />
            <button class="send-btn">Envoyer</button>
          </div>
        </div>
      </div>
    `;
  }

  loadHistory() {
    const stored = localStorage.getItem('chatHistory');
    if (stored) {
      try { this.chatHistory = JSON.parse(stored); } catch { this.chatHistory = []; }
    }
    if (!this.chatHistory.length) {
      this.chatHistory = [
        { role: 'assistant', content: "Bonjour ! Comment puis-je vous aider aujourd'hui avec vos besoins immobiliers en Suisse ?" }
      ];
    }
  }

  saveHistory() {
    localStorage.setItem('chatHistory', JSON.stringify(this.chatHistory));
  }

  renderHistory() {
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    messagesDiv.innerHTML = '';
    for (const msg of this.chatHistory) {
      this.addMessage(msg.content, msg.role);
    }
    setTimeout(() => { window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' }); }, 10);
  }

  addMessage(text, sender) {
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    const row = document.createElement('div');
    row.className = 'msg-row' + (sender === 'user' ? ' user' : '');
    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.style.backgroundImage = sender === 'user'
      ? 'url(https://lh3.googleusercontent.com/aida-public/AB6AXuCwwL6YxNjEeCrIU27YVthQt_1k5aCyW-8chtBVFWCZYoTdhL9uZezEeHZjG-vMY4YTNDdVfn6chpmrwDsYMfdT9SY90fBTSRxxFNElD-YueAgtM8WQ9FI9_GoE__00ZSRGX1TS-OhC6PHDi_ElRE8m60O8zyKBt57QM3nUVsZM-Fz0zl61H7qjZKmyKCK1EHL6u8ww2eziLt-yR5OMOp3d-LIG6uwJq4KtTL-ZBNeSszREXlGuTAA_rRVWxY2tBPzNtZXsqxMpP4_5)'
      : 'url(https://lh3.googleusercontent.com/aida-public/AB6AXuAf-YxrjjkmQgT-xo_C5_RRFc4xqpwQcYbMNLFz7dsSYY4ny2RN2doubGuDarHobC4_yQdvwEZNRZLO5XQfHI7BrLEe06X3uCgAVW4q15v8C_qeTnwKZywKF5Gs81Dxmyz0-4aOuVPYDd78XRDhzW7c618qT520eph-aej0imze6zmSB4z3UpdcMnnxnPGXILV8N4BYLb473vs2vwQSEoG1hVjdNfe8bYB0k8mWxJuTkd89uZIRIchnBxjfnfVWN39yYrF237waojeF);';
    const bubble = document.createElement('div');
    bubble.className = 'bubble ' + (sender === 'user' ? 'user' : 'bot');
    bubble.innerHTML = window.marked ? window.marked.parse(text) : text;
    row.appendChild(avatar);
    row.appendChild(bubble);
    messagesDiv.appendChild(row);
    setTimeout(() => { window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' }); }, 10);
  }

  addListeners() {
    const input = this.shadowRoot.querySelector('.input-box');
    const sendBtn = this.shadowRoot.querySelector('.send-btn');
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.handleSend();
      }
    });
    sendBtn.addEventListener('click', () => this.handleSend());
  }

  handleSend() {
    const input = this.shadowRoot.querySelector('.input-box');
    const text = input.value.trim();
    if (!text) return;
    this.addMessage(text, 'user');
    this.chatHistory.push({ role: 'user', content: text });
    this.saveHistory();
    input.value = '';
    this.sendToBackend();
  }

  async sendToBackend() {
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    // Ajoute une bulle vide pour le streaming
    const row = document.createElement('div');
    row.className = 'msg-row';
    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.style.backgroundImage = 'url(https://lh3.googleusercontent.com/aida-public/AB6AXuAf-YxrjjkmQgT-xo_C5_RRFc4xqpwQcYbMNLFz7dsSYY4ny2RN2doubGuDarHobC4_yQdvwEZNRZLO5XQfHI7BrLEe06X3uCgAVW4q15v8C_qeTnwKZywKF5Gs81Dxmyz0-4aOuVPYDd78XRDhzW7c618qT520eph-aej0imze6zmSB4z3UpdcMnnxnPGXILV8N4BYLb473vs2vwQSEoG1hVjdNfe8bYB0k8mWxJuTkd89uZIRIchnBxjfnfVWN39yYrF237waojeF);';
    const bubble = document.createElement('div');
    bubble.className = 'bubble bot';
    bubble.innerHTML = `<div class="typing-indicator"><span></span><span></span><span></span></div>`;
    row.appendChild(avatar);
    row.appendChild(bubble);
    messagesDiv.appendChild(row);
    setTimeout(() => { window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' }); }, 10);

    // Get selected model from localStorage (dropdown custom)
    let selectedModel = localStorage.getItem('selectedModel') || 'gemini/gemini-2.5-pro';

    // Streaming SSE
    const response = await fetch('/chat/stream', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ messages: this.chatHistory, model: selectedModel })
    });
    if (!response.body) return;
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let botReply = '';
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop();
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            if (data.content) {
              if (botReply === '') bubble.innerHTML = ''; // Clear indicator on first chunk
              botReply += data.content;
              bubble.innerHTML = window.marked ? window.marked.parse(botReply) : botReply;
              setTimeout(() => { window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' }); }, 5);
            }
            if (data.done) {
              if (botReply.trim()) {
                this.chatHistory.push({ role: 'assistant', content: botReply });
                this.saveHistory();
              }
              // If the final bubble is empty, remove it
              if (bubble.innerHTML.trim() === '' || bubble.querySelector('.typing-indicator')) {
                row.remove();
              }
              return;
            }
          } catch {}
        }
      }
    }
  }

  clearHistory() {
    // Clear chat history
    this.chatHistory = [];

    // Clear messages from DOM
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    if (messagesDiv) {
      messagesDiv.innerHTML = '';
    }

    // Clear localStorage
    localStorage.removeItem('chatHistory');

    // Reset to initial state
    this.loadHistory();
    this.renderHistory();
  }
}

customElements.define('chat-widget', ChatWidget);