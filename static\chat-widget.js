class ChatWidget extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
    this.chatHistory = [];
  }

  connectedCallback() {
    this.render();
    this.loadHistory();
    this.renderHistory();
    this.addListeners();
    this.observeDarkMode();
    // Restore last used model
    setTimeout(() => {
      try {
        const modelSelect = window.parent.document.getElementById('model-select');
        if (modelSelect) {
          const last = localStorage.getItem('selectedModel');
          if (last) modelSelect.value = last;
        }
      } catch {}
    }, 100);
  }

  getTheme() {
    // Suivre la classe du body pour le dark mode global
    return document.body.classList.contains('dark-theme') ? 'dark' : 'light';
  }

  observeDarkMode() {
    // Observer les changements de classe sur le body pour le dark mode
    const observer = new MutationObserver(() => {
      this.setTheme(this.getTheme());
    });
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
    this.setTheme(this.getTheme());
  }

  setTheme(theme) {
    const container = this.shadowRoot.querySelector('.container');
    container.classList.remove('light-theme', 'dark-theme');
    container.classList.add(`${theme}-theme`);
  }

  render() {
    this.shadowRoot.innerHTML = `
      <style>
        .light-theme {
          --bg-primary: #ffffff;
          --bg-secondary: #f0f3f4;
          --text-primary: #111518;
          --text-secondary: #637c88;
          --border-color: #f0f3f4;
          --user-message: #19a1e5;
          --bot-message: #f0f3f4;
          --input-bg: #f0f3f4;
          --button-bg: #19a1e5;
        }
        .dark-theme {
          --bg-primary: #111618;
          --bg-secondary: #283339;
          --text-primary: #ffffff;
          --text-secondary: #9db0b9;
          --border-color: #283339;
          --user-message: #1193d4;
          --bot-message: #283339;
          --input-bg: #283339;
          --button-bg: #1193d4;
        }
        .theme-transition { transition: all 0.3s ease; }
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          background: var(--bg-primary);
          border-radius: 18px;
          box-shadow: 0 2px 12px 0 #0001;
          padding-bottom: 32px;
        }
        .main-chat-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          padding-top: 100px;
          padding-bottom: 80px;
        }
        .chatbox { width: 100%; max-width: 896px; display: flex; flex-direction: column; flex: 1; }
        .welcome {
          text-align: center;
          color: var(--text-primary);
          margin-bottom: 12px;
          font-size: 1.8rem;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0,0,0,0.1);
          padding: 0 16px;
        }
        .subtitle { text-align: center; color: var(--text-primary); margin-bottom: 16px; }
        .messages {
          flex: 1;
          padding: 0 8px;
          overflow-y: auto;
          scroll-behavior: smooth;
        }
        .msg-row {
          display: flex;
          gap: 12px;
          margin-bottom: 1rem;
          align-items: flex-start;
          opacity: 0;
          transform: translateY(20px);
          animation: slideInMessage 0.4s ease-out forwards;
        }
        .msg-row.user { flex-direction: row-reverse; }
        .avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-size: cover;
          background-position: center;
          flex-shrink: 0;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          transition: transform 0.2s ease;
        }
        .avatar:hover {
          transform: scale(1.05);
        }
        .bubble {
          border-radius: 20px;
          padding: 14px 20px;
          max-width: 75%;
          font-size: 1rem;
          line-height: 1.6;
          word-break: break-word;
          box-sizing: border-box;
          position: relative;
          box-shadow: 0 2px 12px rgba(0,0,0,0.08);
          transition: all 0.3s ease;
        }
        .bubble:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        .bubble.user {
          background: linear-gradient(135deg, var(--user-message) 0%, #1193d4 100%);
          color: #fff;
          align-self: flex-end;
          padding: 12px 18px;
          border-radius: 20px 20px 4px 20px;
          min-width: 0;
          width: fit-content;
          display: inline-block;
          line-height: 1.5;
          font-size: 0.95rem;
          font-weight: 500;
          box-shadow: 0 3px 15px rgba(25, 161, 229, 0.3);
        }
        .bubble.bot {
          background: var(--bot-message);
          color: var(--text-primary);
          align-self: flex-start;
          border-radius: 20px 20px 20px 4px;
          border: 1px solid rgba(0,0,0,0.05);
        }
        .bubble :is(h1,h2,h3,h4,h5,h6) {
          margin: 0.8em 0 0.4em 0;
          font-weight: 600;
          color: var(--text-primary);
        }
        .bubble ul, .bubble ol {
          margin: 0.8em 0 0.8em 1.5em;
          padding-left: 0.5em;
        }
        .bubble li {
          margin-bottom: 0.3em;
        }
        .bubble pre {
          background: rgba(0,0,0,0.05);
          border-radius: 12px;
          padding: 12px 16px;
          overflow-x: auto;
          margin: 0.8em 0;
          border: 1px solid rgba(0,0,0,0.1);
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }
        .bubble code {
          background: rgba(0,0,0,0.08);
          border-radius: 6px;
          padding: 3px 6px;
          font-size: 0.9em;
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }
        .bubble p {
          margin: 0.6em 0;
        }
        .bubble p:first-child {
          margin-top: 0;
        }
        .bubble p:last-child {
          margin-bottom: 0;
        }
        .typing-indicator {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 8px 0;
        }
        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-secondary);
            animation: geminiTyping 1.4s ease-in-out infinite both;
        }
        .typing-indicator span:nth-child(1) { animation-delay: 0s; }
        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

        /* Animations inspirées de Gemini */
        @keyframes geminiTyping {
            0%, 80%, 100% {
              transform: scale(0.8);
              opacity: 0.5;
            }
            40% {
              transform: scale(1.2);
              opacity: 1;
            }
        }

        @keyframes slideInMessage {
            0% {
              opacity: 0;
              transform: translateY(20px) scale(0.95);
            }
            100% {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
        }

        @keyframes fadeInText {
            0% {
              opacity: 0;
            }
            100% {
              opacity: 1;
            }
        }

        .bubble.bot.streaming {
          animation: fadeInText 0.3s ease-out;
        }
        .input-bar {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          width: 100%;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          border-top: 1px solid rgba(0,0,0,0.08);
          z-index: 50;
          padding: 16px 0 20px 0;
          box-shadow: 0 -4px 20px rgba(0,0,0,0.08);
          transition: all 0.3s ease;
        }
        .dark-theme .input-bar {
          background: rgba(17, 22, 24, 0.95);
          border-top: 1px solid rgba(255,255,255,0.1);
        }
        .input-inner {
          max-width: 896px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          padding: 0 16px;
          gap: 12px;
        }
        .input-box {
          flex: 1;
          border-radius: 28px;
          background: var(--input-bg);
          border: 2px solid transparent;
          padding: 0 24px;
          height: 56px;
          font-size: 1rem;
          color: var(--text-primary);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          min-width: 0;
          box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }
        .input-box:focus {
          outline: none;
          border-color: var(--button-bg);
          box-shadow: 0 0 0 4px rgba(25, 161, 229, 0.12), 0 4px 16px rgba(0,0,0,0.08);
          transform: translateY(-1px);
        }
        .input-box::placeholder {
          color: var(--text-secondary);
          font-weight: 400;
        }
        .send-btn {
          background: linear-gradient(135deg, var(--button-bg) 0%, #1193d4 100%);
          color: #fff;
          border: none;
          border-radius: 28px;
          padding: 0 28px;
          height: 56px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          flex-shrink: 0;
          min-width: 100px;
          box-shadow: 0 4px 12px rgba(25, 161, 229, 0.25);
          position: relative;
          overflow: hidden;
        }
        .send-btn::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s;
        }
        .send-btn:hover::before {
          left: 100%;
        }
        .send-btn:hover {
          background: linear-gradient(135deg, #1193d4 0%, #0d7ab8 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(25, 161, 229, 0.4);
        }
        .send-btn:active {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(25, 161, 229, 0.3);
        }
        .send-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: 0 2px 8px rgba(25, 161, 229, 0.15);
        }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .main-chat-area {
            padding-top: 80px;
            padding-bottom: 100px;
            padding-left: 8px;
            padding-right: 8px;
          }
          .welcome {
            font-size: 1.5rem;
            padding: 0 12px;
          }
          .subtitle {
            font-size: 0.9rem;
            padding: 0 12px;
          }
          .input-inner {
            padding: 0 12px;
            gap: 8px;
          }
          .input-box {
            height: 44px;
            font-size: 0.95rem;
            padding: 0 16px;
          }
          .send-btn {
            height: 44px;
            padding: 0 20px;
            font-size: 0.9rem;
            min-width: 70px;
          }
        }

        @media (max-width: 480px) {
          .main-chat-area {
            padding-top: 70px;
            padding-bottom: 90px;
            padding-left: 4px;
            padding-right: 4px;
          }
          .welcome {
            font-size: 1.3rem;
            padding: 0 8px;
          }
          .subtitle {
            font-size: 0.85rem;
            padding: 0 8px;
          }
          .input-inner {
            padding: 0 8px;
            gap: 6px;
          }
          .input-box {
            height: 40px;
            font-size: 0.9rem;
            padding: 0 14px;
            border-radius: 20px;
          }
          .send-btn {
            height: 40px;
            padding: 0 16px;
            font-size: 0.85rem;
            min-width: 60px;
            border-radius: 20px;
          }
        }
      </style>
      <div class="container theme-transition">
        <div class="main-chat-area">
          <div class="chatbox">
            <h2 class="welcome">Bienvenue sur Swiss Estate AI</h2>
            <p class="subtitle">Posez-moi toutes vos questions sur le marché immobilier suisse. Je peux vous aider à trouver des propriétés, à comprendre les tendances du marché, et bien plus encore.</p>
            <div class="messages"></div>
          </div>
        </div>
        <div class="input-bar">
          <div class="input-inner">
            <input class="input-box" type="text" placeholder="Tapez votre question ici..." />
            <button class="send-btn">Envoyer</button>
          </div>
        </div>
      </div>
    `;
  }

  loadHistory() {
    const stored = localStorage.getItem('chatHistory');
    if (stored) {
      try { this.chatHistory = JSON.parse(stored); } catch { this.chatHistory = []; }
    }
    if (!this.chatHistory.length) {
      this.chatHistory = [
        { role: 'assistant', content: "Bonjour ! Comment puis-je vous aider aujourd'hui avec vos besoins immobiliers en Suisse ?" }
      ];
    }
  }

  saveHistory() {
    localStorage.setItem('chatHistory', JSON.stringify(this.chatHistory));
  }

  renderHistory() {
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    messagesDiv.innerHTML = '';

    // Rendre l'historique avec des animations échelonnées
    this.chatHistory.forEach((msg, index) => {
      setTimeout(() => {
        this.addMessage(msg.content, msg.role);
      }, index * 80); // Délai échelonné pour un effet fluide
    });

    // Scroll final après que tous les messages soient rendus
    setTimeout(() => {
      this.smoothScrollToBottom();
    }, this.chatHistory.length * 80 + 200);
  }

  addMessage(text, sender) {
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    const row = document.createElement('div');
    row.className = 'msg-row' + (sender === 'user' ? ' user' : '');

    // Commencer avec l'état initial pour l'animation
    row.style.opacity = '0';
    row.style.transform = 'translateY(20px) scale(0.95)';

    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.style.backgroundImage = sender === 'user'
      ? 'url(https://lh3.googleusercontent.com/aida-public/AB6AXuCwwL6YxNjEeCrIU27YVthQt_1k5aCyW-8chtBVFWCZYoTdhL9uZezEeHZjG-vMY4YTNDdVfn6chpmrwDsYMfdT9SY90fBTSRxxFNElD-YueAgtM8WQ9FI9_GoE__00ZSRGX1TS-OhC6PHDi_ElRE8m60O8zyKBt57QM3nUVsZM-Fz0zl61H7qjZKmyKCK1EHL6u8ww2eziLt-yR5OMOp3d-LIG6uwJq4KtTL-ZBNeSszREXlGuTAA_rRVWxY2tBPzNtZXsqxMpP4_5)'
      : 'url(https://lh3.googleusercontent.com/aida-public/AB6AXuAf-YxrjjkmQgT-xo_C5_RRFc4xqpwQcYbMNLFz7dsSYY4ny2RN2doubGuDarHobC4_yQdvwEZNRZLO5XQfHI7BrLEe06X3uCgAVW4q15v8C_qeTnwKZywKF5Gs81Dxmyz0-4aOuVPYDd78XRDhzW7c618qT520eph-aej0imze6zmSB4z3UpdcMnnxnPGXILV8N4BYLb473vs2vwQSEoG1hVjdNfe8bYB0k8mWxJuTkd89uZIRIchnBxjfnfVWN39yYrF237waojeF);';

    const bubble = document.createElement('div');
    bubble.className = 'bubble ' + (sender === 'user' ? 'user' : 'bot');
    bubble.innerHTML = window.marked ? window.marked.parse(text) : text;

    row.appendChild(avatar);
    row.appendChild(bubble);
    messagesDiv.appendChild(row);

    // Animation d'apparition fluide
    setTimeout(() => {
      row.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
      row.style.opacity = '1';
      row.style.transform = 'translateY(0) scale(1)';
    }, 50);

    // Scroll fluide avec délai
    setTimeout(() => {
      this.smoothScrollToBottom();
    }, 100);
  }

  addListeners() {
    const input = this.shadowRoot.querySelector('.input-box');
    const sendBtn = this.shadowRoot.querySelector('.send-btn');
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.handleSend();
      }
    });
    sendBtn.addEventListener('click', () => this.handleSend());
  }

  handleSend() {
    const input = this.shadowRoot.querySelector('.input-box');
    const text = input.value.trim();
    if (!text) return;
    this.addMessage(text, 'user');
    this.chatHistory.push({ role: 'user', content: text });
    this.saveHistory();
    input.value = '';
    this.sendToBackend();
  }

  async sendToBackend() {
    const messagesDiv = this.shadowRoot.querySelector('.messages');

    // Créer la bulle de réponse avec animation
    const row = document.createElement('div');
    row.className = 'msg-row';

    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.style.backgroundImage = 'url(https://lh3.googleusercontent.com/aida-public/AB6AXuAf-YxrjjkmQgT-xo_C5_RRFc4xqpwQcYbMNLFz7dsSYY4ny2RN2doubGuDarHobC4_yQdvwEZNRZLO5XQfHI7BrLEe06X3uCgAVW4q15v8C_qeTnwKZywKF5Gs81Dxmyz0-4aOuVPYDd78XRDhzW7c618qT520eph-aej0imze6zmSB4z3UpdcMnnxnPGXILV8N4BYLb473vs2vwQSEoG1hVjdNfe8bYB0k8mWxJuTkd89uZIRIchnBxjfnfVWN39yYrF237waojeF);';

    const bubble = document.createElement('div');
    bubble.className = 'bubble bot streaming';
    bubble.innerHTML = `<div class="typing-indicator"><span></span><span></span><span></span></div>`;

    row.appendChild(avatar);
    row.appendChild(bubble);
    messagesDiv.appendChild(row);

    // Animation d'apparition fluide
    setTimeout(() => {
      row.style.opacity = '1';
      row.style.transform = 'translateY(0) scale(1)';
    }, 50);

    // Scroll fluide avec délai
    setTimeout(() => {
      this.smoothScrollToBottom();
    }, 100);

    // Get selected model from localStorage (dropdown custom)
    let selectedModel = localStorage.getItem('selectedModel') || 'gemini/gemini-2.5-pro';

    // Streaming SSE
    const response = await fetch('/chat/stream', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ messages: this.chatHistory, model: selectedModel })
    });
    if (!response.body) return;
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let botReply = '';
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop();
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            if (data.content) {
              if (botReply === '') {
                // Premier chunk - supprimer l'indicateur de frappe avec animation
                bubble.style.transition = 'opacity 0.2s ease';
                bubble.style.opacity = '0.7';
                setTimeout(() => {
                  bubble.innerHTML = '';
                  bubble.style.opacity = '1';
                  bubble.style.transition = '';
                }, 150);
              }
              botReply += data.content;

              // Mise à jour du contenu avec animation de fade
              const newContent = window.marked ? window.marked.parse(botReply) : botReply;
              bubble.innerHTML = newContent;

              // Scroll fluide pendant le streaming
              this.smoothScrollToBottom();
            }
            if (data.done) {
              if (botReply.trim()) {
                this.chatHistory.push({ role: 'assistant', content: botReply });
                this.saveHistory();
              }
              // If the final bubble is empty, remove it
              if (bubble.innerHTML.trim() === '' || bubble.querySelector('.typing-indicator')) {
                row.remove();
              }
              return;
            }
          } catch {}
        }
      }
    }
  }

  smoothScrollToBottom() {
    // Scroll fluide vers le bas avec easing personnalisé
    const scrollContainer = window;
    const targetY = document.body.scrollHeight;
    const startY = scrollContainer.scrollY;
    const distance = targetY - startY;
    const duration = 300;
    let start = null;

    function step(timestamp) {
      if (!start) start = timestamp;
      const progress = Math.min((timestamp - start) / duration, 1);

      // Easing function (ease-out-cubic)
      const easeProgress = 1 - Math.pow(1 - progress, 3);

      scrollContainer.scrollTo(0, startY + distance * easeProgress);

      if (progress < 1) {
        requestAnimationFrame(step);
      }
    }

    requestAnimationFrame(step);
  }

  clearHistory() {
    // Clear chat history
    this.chatHistory = [];

    // Clear messages from DOM avec animation
    const messagesDiv = this.shadowRoot.querySelector('.messages');
    if (messagesDiv) {
      // Animation de disparition
      const messages = messagesDiv.querySelectorAll('.msg-row');
      messages.forEach((msg, index) => {
        setTimeout(() => {
          msg.style.transition = 'all 0.3s ease';
          msg.style.opacity = '0';
          msg.style.transform = 'translateY(-20px) scale(0.95)';
        }, index * 50);
      });

      // Nettoyer après l'animation
      setTimeout(() => {
        messagesDiv.innerHTML = '';
      }, messages.length * 50 + 300);
    }

    // Clear localStorage
    localStorage.removeItem('chatHistory');

    // Reset to initial state
    setTimeout(() => {
      this.loadHistory();
      this.renderHistory();
    }, 500);
  }
}

customElements.define('chat-widget', ChatWidget);