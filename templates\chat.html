<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Swiss Estate AI - Assistant <PERSON><PERSON><PERSON><PERSON><PERSON></title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" />
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script type="module" src="/static/chat-widget.js"></script>
    
    <style>
        body { font-family: Inter, "Noto Sans", sans-serif; }
        
        /* Light theme */
        .light-theme {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f3f4;
            --text-primary: #111518;
            --text-secondary: #637c88;
            --border-color: #f0f3f4;
            --user-message: #19a1e5;
            --bot-message: #f0f3f4;
            --input-bg: #f0f3f4;
            --button-bg: #19a1e5;
        }
        
        /* Dark theme */
        .dark-theme {
            --bg-primary: #111618;
            --bg-secondary: #283339;
            --text-primary: #ffffff;
            --text-secondary: #9db0b9;
            --border-color: #283339;
            --user-message: #1193d4;
            --bot-message: #283339;
            --input-bg: #283339;
            --button-bg: #1193d4;
        }
        
        .theme-transition {
            transition: all 0.3s ease;
        }
        
        .user-message { background: var(--user-message); color: white; }
        .bot-message { background: var(--bot-message); color: var(--text-primary); }
        .input-container { background: var(--input-bg); border-radius: 12px; }
        .send-button { background: var(--button-bg); color: white; }
        .send-button:hover { opacity: 0.9; }
    </style>
</head>
<body class="light-theme theme-transition">
    <div class="relative flex flex-col min-h-screen w-full" style="background-color: var(--bg-primary);">
        <!-- Header -->
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid px-4 md:px-10 py-3 theme-transition fixed top-0 left-0 w-full z-30 shadow-md" style="border-color: var(--border-color); background: var(--bg-primary);">
                <div class="flex items-center gap-4" style="color: var(--text-primary);">
                    <div class="size-4">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 6H42L36 24L42 42H6L12 24L6 6Z" fill="currentColor"></path>
                        </svg>
                    </div>
                    <h2 class="text-base sm:text-lg font-bold leading-tight tracking-[-0.015em]" style="color: var(--text-primary);">
                        <span class="hidden xs:inline">Swiss Estate AI</span>
                        <span class="xs:hidden">Swiss AI</span>
                    </h2>
                </div>
                <div class="flex flex-1 justify-end gap-2 sm:gap-4 md:gap-8">

                    <!-- New Conversation Button -->
                    <button id="new-conversation-btn" class="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-transparent text-[var(--text-primary)] font-medium shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-2 focus:ring-blue-300 transition-all duration-200 text-xs sm:text-sm"
                        title="Nouvelle conversation">
                        <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                        </svg>
                        <span class="hidden sm:inline">Nouveau</span>
                    </button>

                    <!-- Model Selector - Responsive -->
                    <div class="relative" id="model-dropdown-root">
                      <button id="model-dropdown-btn" type="button"
                        class="model-dropdown-btn flex items-center gap-2 px-4 py-2 rounded-xl border-2 border-blue-400 bg-[var(--bg-secondary)] text-[var(--text-primary)] font-semibold shadow-md hover:bg-blue-100 focus:ring-2 focus:ring-blue-300 transition-all duration-200"
                        aria-haspopup="listbox" aria-expanded="false">
                        <span id="model-selected-label" class="model-selected-label">Gemini 2.5 Pro</span>
                        <svg id="model-dropdown-arrow" class="model-dropdown-arrow w-4 h-4 ml-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      <ul id="model-dropdown-list"
                        class="model-dropdown-list absolute right-0 mt-2 w-52 bg-white dark:bg-[#222] border border-blue-200 rounded-xl shadow-2xl z-50 opacity-0 scale-95 pointer-events-none transition-all duration-200 origin-top min-w-[180px]">
                        <li class="model-dropdown-item flex items-center gap-2 px-4 py-2 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900 transition font-medium rounded-lg"
                            data-value="gemini/gemini-2.5-pro" data-label="Gemini 2.5 Pro" data-logo="🟣">
                          Gemini 2.5 Pro
                        </li>
                        <li class="model-dropdown-item flex items-center gap-2 px-4 py-2 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900 transition font-medium rounded-lg"
                            data-value="mistral/mistral-tiny" data-label="Mistral Tiny" data-logo="🟠">
                          Mistral Tiny
                        </li>
                      </ul>
                    </div>
                    
                    <!-- Theme Toggle Button -->
                    <button id="theme-toggle" class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5 theme-transition" style="background: var(--bg-secondary); color: var(--text-primary);">
                        <div data-icon="Theme" data-size="20px" data-weight="regular" style="color: var(--text-primary);">
                            <svg id="sun-icon" xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256" class="hidden">
                                <path d="M128,56a72,72,0,1,0,72,72A72.08,72.08,0,0,0,128,56Zm0,128a56,56,0,1,1,56-56A56.06,56.06,0,0,1,128,184ZM128,40a8,8,0,0,0,8-8V8a8,8,0,0,0-16,0V32A8,8,0,0,0,128,40ZM128,216a8,8,0,0,0-8,8v24a8,8,0,0,0,16,0V224A8,8,0,0,0,128,216ZM56,128a8,8,0,0,0-8-8H8a8,8,0,0,0,0,16H48A8,8,0,0,0,56,128ZM248,120H208a8,8,0,0,0,0,16h40a8,8,0,0,0,0-16ZM37.66,66.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32-11.32Zm150.68,150.68a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32-11.32ZM66.34,37.66a8,8,0,0,0,11.32-11.32l-16-16a8,8,0,0,0-11.32,11.32Zm150.68,150.68a8,8,0,0,0,11.32-11.32l-16-16a8,8,0,0,0-11.32,11.32Z"></path>
                            </svg>
                            <svg id="moon-icon" xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M240,96a8,8,0,0,1-8,8H216v16a8,8,0,0,1-16,0V104H184a8,8,0,0,1,0-16h16V72a8,8,0,0,1,16,0V88h16A8,8,0,0,1,240,96ZM144,56a8,8,0,0,0,8-8V40a8,8,0,0,0-16,0v8A8,8,0,0,0,144,56Zm72,96a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-8A8,8,0,0,0,216,152ZM128,200a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-8A8,8,0,0,0,128,200Zm-96-96a8,8,0,0,0-8-8H8a8,8,0,0,0,0,16h16A8,8,0,0,0,32,104Zm8,72H24a8,8,0,0,0,0,16h16a8,8,0,0,0,0-16ZM40,56a8,8,0,0,0,8-8V40a8,8,0,0,0-16,0v8A8,8,0,0,0,40,56ZM128,24a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0V32A8,8,0,0,0,128,24ZM78.34,78.34a8,8,0,0,0,11.32,0l11.31-11.31a8,8,0,0,0-11.32-11.32L78.34,67A8,8,0,0,0,78.34,78.34Zm100.68,100.68a8,8,0,0,0-11.32,0L156.69,189.66a8,8,0,0,0,11.32,11.32l11.31-11.31A8,8,0,0,0,179,179Zm-11.31-100.68a8,8,0,0,0,0-11.32L156.69,56.69a8,8,0,0,0-11.32,11.32l11.31,11.31A8,8,0,0,0,167.69,78.34ZM78.34,167.69a8,8,0,0,0-11.32,0L55.71,179a8,8,0,0,0,11.32,11.32L78.34,179A8,8,0,0,0,78.34,167.69ZM128,184a56,56,0,1,1,56-56A56.06,56.06,0,0,1,128,184Z"></path>
                            </svg>
                        </div>
                    </button>
                    
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCwwL6YxNjEeCrIU27YVthQt_1k5aCyW-8chtBVFWCZYoTdhL9uZezEeHZjG-vMY4YTNDdVfn6chpmrwDsYMfdT9SY90fBTSRxxFNElD-YueAgtM8WQ9FI9_GoE__00ZSRGX1TS-OhC6PHDi_ElRE8m60O8zyKBt57QM3nUVsZM-Fz0zl61H7qjZKmyKCK1EHL6u8ww2eziLt-yR5OMOp3d-LIG6uwJq4KtTL-ZBNeSszREXlGuTAA_rRVWxY2tBPzNtZXsqxMpP4_5");'></div>
                </div>
            </header>
        <chat-widget></chat-widget>
    </div>

    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Note: chat functionality is handled by the chat-widget component
            const themeToggle = document.getElementById('theme-toggle');
            const sunIcon = document.getElementById('sun-icon');
            const moonIcon = document.getElementById('moon-icon');

            // Chat history is managed by the chat-widget component

            // Theme management
            function setTheme(theme) {
                const body = document.body;
                if (theme === 'dark') {
                    body.className = 'dark-theme theme-transition';
                    if (sunIcon) sunIcon.classList.remove('hidden');
                    if (moonIcon) moonIcon.classList.add('hidden');
                } else {
                    body.className = 'light-theme theme-transition';
                    if (sunIcon) sunIcon.classList.add('hidden');
                    if (moonIcon) moonIcon.classList.remove('hidden');
                }
                localStorage.setItem('theme', theme);
            }

            // Initialize theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            setTheme(savedTheme);

            // Theme toggle
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    const currentTheme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    setTheme(newTheme);
                });
            }

            // New conversation button
            const newConversationBtn = document.getElementById('new-conversation-btn');
            if (newConversationBtn) {
                newConversationBtn.addEventListener('click', () => {
                    // Clear chat history in the widget
                    const chatWidget = document.querySelector('chat-widget');
                    if (chatWidget && chatWidget.clearHistory) {
                        chatWidget.clearHistory();
                    }

                    // Clear localStorage chat history
                    localStorage.removeItem('chatHistory');

                    // Reload the page to reset everything
                    window.location.reload();
                });
            }

            // Message handling is done by the chat-widget component

            // Chat form submission is handled by the chat-widget component
        });
    </script>
    <script>
      // Dropdown Gemini style amélioré
      document.addEventListener('DOMContentLoaded', function () {
        const btn = document.querySelector('#model-dropdown-btn.model-dropdown-btn');
        const list = document.querySelector('#model-dropdown-list.model-dropdown-list');
        const label = document.querySelector('#model-selected-label.model-selected-label');
        const arrow = document.querySelector('#model-dropdown-arrow.model-dropdown-arrow');
        let open = false;
        // --- Correction : stocke la valeur par défaut au chargement si absente ---
        const defaultValue = 'gemini/gemini-2.5-pro';
        if (!localStorage.getItem('selectedModel')) {
          localStorage.setItem('selectedModel', defaultValue);
        }
        // --- Fin correction ---
        btn.onclick = (e) => {
          e.stopPropagation();
          open = !open;
          if (open) {
            list.classList.remove('opacity-0', 'scale-95', 'pointer-events-none');
            list.classList.add('opacity-100', 'scale-100');
            arrow.style.transform = 'rotate(180deg)';
          } else {
            list.classList.add('opacity-0', 'scale-95', 'pointer-events-none');
            list.classList.remove('opacity-100', 'scale-100');
            arrow.style.transform = 'rotate(0deg)';
          }
        };
        list.querySelectorAll('li.model-dropdown-item').forEach(item => {
          item.onclick = () => {
            const value = item.getAttribute('data-value');
            const text = item.getAttribute('data-label');
            const icon = item.getAttribute('data-logo');
            label.textContent = text;
            open = false;
            list.classList.add('opacity-0', 'scale-95', 'pointer-events-none');
            list.classList.remove('opacity-100', 'scale-100');
            arrow.style.transform = 'rotate(0deg)';
            // --- Correction : stocke la valeur à chaque sélection ---
            localStorage.setItem('selectedModel', value);
            console.log('Model saved to localStorage:', value);
            // --- Fin correction ---
            document.dispatchEvent(new CustomEvent('modelChanged', { detail: { value } }));
          };
        });
        document.body.onclick = () => {
          open = false;
          list.classList.add('opacity-0', 'scale-95', 'pointer-events-none');
          list.classList.remove('opacity-100', 'scale-100');
          arrow.style.transform = 'rotate(0deg)';
        };
        // Restore last used model
        const last = localStorage.getItem('selectedModel');
        if (last) {
          const item = Array.from(list.querySelectorAll('li.model-dropdown-item')).find(li => li.getAttribute('data-value') === last);
          if (item) {
            label.textContent = item.getAttribute('data-label');
            console.log('Model restored from localStorage:', last);
          }
        }
      });
    </script>
</body>
</html> 