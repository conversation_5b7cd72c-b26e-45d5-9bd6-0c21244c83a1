#!/usr/bin/env python3
"""
Service unifié de scraping pour les données hypothécaires suisses
Combine BeautifulSoup et Selenium de manière optimisée
"""

import random
import requests
from bs4 import BeautifulSoup
import sqlite3
import json
import time
from datetime import datetime, timedelta
import logging
import re
from typing import List, Dict, Optional, Union

# Configuration Selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.keys import Keys
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import chromedriver_autoinstaller
    chromedriver_autoinstaller.install()
except ImportError:
    pass

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mortgage_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SwissMortgageScraper:
    def __init__(self, db_path: str = "mortgage_data.db", headless: bool = True):
        """Initialise le scraper avec la base de données"""
        self.db_path = db_path
        self.headless = headless
        self.driver = None
        self.init_database()
        
    def init_database(self) -> None:
        """Initialise toutes les tables nécessaires"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Crée toutes les tables en une seule transaction
        cursor.executescript('''
            CREATE TABLE IF NOT EXISTS scraping_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT NOT NULL,
                method TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                error_message TEXT,
                response_time REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE IF NOT EXISTS mortgage_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                duration TEXT NOT NULL,
                rate REAL NOT NULL,
                bank TEXT,
                source_url TEXT,
                confidence_score REAL DEFAULT 1.0,
                method TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE IF NOT EXISTS mortgage_simulations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                loan_amount REAL NOT NULL,
                duration_years INTEGER NOT NULL,
                monthly_payment REAL,
                total_cost REAL,
                interest_rate REAL,
                source_url TEXT,
                calculation_method TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        conn.commit()
        conn.close()
        logger.info("Base de données initialisée")
    
    def init_selenium_driver(self) -> None:
        chrome_options = Options()
        
        # Enhanced stealth options
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Randomize user agent
        user_agents = [
            # Add multiple user agents here
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        chrome_options.add_argument(f"user-agent={random.choice(user_agents)}")
        
        # Other options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        if self.headless:
            chrome_options.add_argument("--headless=new")  # Use new headless mode
            
        self.driver = webdriver.Chrome(options=chrome_options)
        
        # Execute stealth JS
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": chrome_options.arguments[-1].split('=')[1]})
    
    def close_selenium(self) -> None:
        """Ferme proprement le driver Selenium"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            logger.info("Navigateur Selenium fermé")
    
    def log_scraping_attempt(
        self,
        url: str,
        method: str,
        success: bool,
        error_message: str = None,
        response_time: float = None
    ) -> None:
        """Enregistre une tentative de scraping"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO scraping_logs (url, method, success, error_message, response_time)
            VALUES (?, ?, ?, ?, ?)
        ''', (url, method, success, error_message, response_time))
        
        conn.commit()
        conn.close()
    
    def get_page_with_selenium(self, url: str, wait_for: str = "body", timeout: int = 10) -> Optional[str]:
        """Récupère le HTML rendu avec Selenium"""
        if not self.driver:
            self.init_selenium_driver()
        
        try:
            start_time = time.time()
            self.driver.get(url)
            
            # Attendre que la page se charge
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, wait_for))
            )
            
            # Simuler un comportement humain
            self.simulate_human_behavior()
            
            html = self.driver.page_source
            response_time = time.time() - start_time
            
            self.log_scraping_attempt(
                url=url,
                method="selenium",
                success=True,
                response_time=response_time
            )
            
            return html
            
        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            self.log_scraping_attempt(
                url=url,
                method="selenium",
                success=False,
                error_message=str(e),
                response_time=response_time
            )
            logger.error(f"Erreur Selenium: {e}")
            return None
    
    def get_page_with_requests(self, url: str) -> Optional[str]:
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept-Language': 'fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Referer': 'https://www.google.com/',
                'DNT': '1'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.error(f"Erreur requests: {e}")
            return None
        
    def simulate_human_behavior(self) -> None:
        """Simule un comportement humain pour éviter la détection"""
        try:
            # Scroll progressif
            for i in range(3):
                self.driver.execute_script(f"window.scrollTo(0, {i * 300});")
                time.sleep(0.5)
            
            # Retour en haut
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
            logger.debug("Comportement humain simulé")
        except Exception as e:
            logger.warning(f"Erreur simulation comportement: {e}")
    
    def scrape_moneypark_rates(self, use_selenium: bool = False) -> List[Dict]:
        """Version améliorée avec détection des nouveaux sélecteurs"""
        url = "https://moneypark.ch/fr/hypotheque/taux-interet-hypothecaires-actuels/"
        
        try:
            html = self.get_page_with_selenium(url) if use_selenium else self.get_page_with_requests(url)
            if not html:
                raise ValueError("Échec de récupération du contenu HTML")
                
            soup = BeautifulSoup(html, 'html.parser')
            rates = []

            # Nouveaux sélecteurs pour MoneyPark 2023
            rate_sections = soup.find_all('div', class_=re.compile(r'mortgage-rate-wrapper|rate-section'))
            
            for section in rate_sections:
                # Détection des durées
                duration_elems = section.find_all('div', class_=re.compile(r'duration|periode|year'))
                # Détection des taux
                rate_elems = section.find_all('span', class_=re.compile(r'rate-value|percentage'))
                
                for duration_elem, rate_elem in zip(duration_elems, rate_elems):
                    try:
                        duration_text = duration_elem.get_text(strip=True)
                        rate_text = rate_elem.get_text(strip=True)
                        
                        # Extraction des valeurs numériques
                        duration = int(re.search(r'\d+', duration_text).group())
                        rate = float(re.search(r'[\d,]+', rate_text.replace(',', '.')).group())
                        
                        if 1 <= duration <= 30 and 0.1 <= rate <= 10:
                            rates.append({
                                'duration': f"{duration} ans",
                                'rate': rate,
                                'bank': 'MoneyPark',
                                'confidence_score': 0.95,
                                'timestamp': datetime.now()
                            })
                    except (AttributeError, ValueError) as e:
                        logger.debug(f"Erreur parsing: {e}")
                        continue

            if not rates:
                # Fallback aux anciennes méthodes si rien trouvé
                rates = self._extract_rates_from_tables(soup)
                rates.extend(self._extract_rates_from_elements(soup))
                
            if rates:
                self._save_rates_to_db(rates, url, "selenium" if use_selenium else "requests")
                return self._clean_rates_data(rates)
                
            return self._get_fallback_rates(url)
            
        except Exception as e:
            logger.error(f"Erreur scraping: {e}")
            return self._get_fallback_rates(url)
    
    def _extract_rates_from_tables(self, soup: BeautifulSoup) -> List[Dict]:
        """Extrait les taux depuis les tableaux HTML (adapté pour MoneyPark 2024)"""
        rates = []
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) == 2:
                    duration_text = cells[0].get_text(strip=True)
                    rate_text = cells[1].get_text(strip=True)
                    # Exemple: duration_text = 'Taux fixe à 2 ans dès', rate_text = '0.87 %'
                    # Extraire la durée
                    match_duree = re.search(r'(\d+)\s*ans?', duration_text)
                    match_taux = re.search(r'([\d,.]+)', rate_text.replace(',', '.'))
                    if match_duree and match_taux:
                        try:
                            duration = int(match_duree.group(1))
                            rate = float(match_taux.group(1))
                            if 0.5 <= rate <= 10 and 1 <= duration <= 50:
                                rates.append({
                                    'duration': f"{duration} ans",
                                    'rate': rate,
                                    'bank': 'MoneyPark',
                                    'confidence_score': 0.95,
                                    'timestamp': datetime.now()
                                })
                        except Exception as e:
                            logger.debug(f"Erreur parsing tableau: {e}")
                            continue
        logger.debug(f"Taux extraits des tableaux (nouveau parser): {len(rates)}")
        return rates
    
    def _extract_rates_from_elements(self, soup: BeautifulSoup) -> List[Dict]:
        """Extrait les taux depuis des éléments spécifiques"""
        rates = []
        selectors = [
            ".rate", ".interest-rate", ".mortgage-rate",
            "[class*='rate']", "[class*='interest']", "[class*='mortgage']"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                if '%' in text and any(str(i) in text for i in range(1, 51)):
                    rates.extend(self._parse_rate_text(text, confidence=0.8))
        
        logger.debug(f"Taux extraits des éléments: {len(rates)}")
        return rates
    
    def _extract_rates_from_text(self, text: str) -> List[Dict]:
        """Extrait les taux depuis le texte brut"""
        rates = self._parse_rate_text(text, confidence=0.7)
        logger.debug(f"Taux extraits du texte: {len(rates)}")
        return rates
    
    def _parse_rate_text(self, text: str, confidence: float = 0.7) -> List[Dict]:
        """Parse le texte pour trouver des taux hypothécaires"""
        rates = []
        patterns = [
            r'(\d+[,.]?\d*)%\s*(?:pour\s*)?(\d+)\s*ans?',
            r'(\d+)\s*ans?\s*[:\-]?\s*(\d+[,.]?\d*)%',
            r'taux\s*(?:de\s*)?(\d+[,.]?\d*)%\s*(?:sur\s*)?(\d+)\s*ans?',
            r'hypothèque\s*(?:à\s*)?(\d+[,.]?\d*)%\s*(?:sur\s*)?(\d+)\s*ans?'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) == 2:
                    try:
                        rate = float(match.group(1).replace(',', '.'))
                        duration = int(match.group(2))
                        
                        if 0.5 <= rate <= 10 and 1 <= duration <= 50:
                            rates.append({
                                'duration': f"{duration} ans",
                                'rate': rate,
                                'bank': 'MoneyPark',
                                'confidence_score': confidence,
                                'timestamp': datetime.now()
                            })
                    except ValueError:
                        continue
        
        return rates
    
    def _clean_rates_data(self, rates_data: List[Dict]) -> List[Dict]:
        """Nettoie et déduplique les données de taux"""
        unique_rates = {}
        
        for rate in rates_data:
            key = f"{rate['duration']}_{rate['bank']}"
            
            if key not in unique_rates:
                unique_rates[key] = rate
            else:
                # Garder le taux avec le score de confiance le plus élevé
                if rate['confidence_score'] > unique_rates[key]['confidence_score']:
                    unique_rates[key] = rate
        
        # Trier par durée
        sorted_rates = sorted(unique_rates.values(), key=lambda x: int(x['duration'].split()[0]))
        return sorted_rates
    
    def _save_rates_to_db(self, rates_data: List[Dict], source_url: str, method: str) -> None:
        """Sauvegarde les taux en base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for rate in rates_data:
            cursor.execute('''
                INSERT INTO mortgage_rates 
                (duration, rate, bank, source_url, confidence_score, method, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                rate['duration'],
                rate['rate'],
                rate['bank'],
                source_url,
                rate['confidence_score'],
                method,
                rate['timestamp']
            ))
        
        conn.commit()
        conn.close()
        logger.info("Taux sauvegardés en base de données")
    
    def _get_fallback_rates(self, source_url: str) -> List[Dict]:
        """Retourne des taux par défaut en cas d'échec"""
        default_rates = [
            {'duration': '5 ans', 'rate': 3.2, 'bank': 'MoneyPark', 'confidence_score': 0.1, 'timestamp': datetime.now()},
            {'duration': '10 ans', 'rate': 3.5, 'bank': 'MoneyPark', 'confidence_score': 0.1, 'timestamp': datetime.now()},
            {'duration': '15 ans', 'rate': 3.8, 'bank': 'MoneyPark', 'confidence_score': 0.1, 'timestamp': datetime.now()},
            {'duration': '20 ans', 'rate': 4.0, 'bank': 'MoneyPark', 'confidence_score': 0.1, 'timestamp': datetime.now()}
        ]
        
        self._save_rates_to_db(default_rates, source_url, "fallback")
        logger.warning("Utilisation des taux par défaut")
        return default_rates
    
    def get_latest_rates(self) -> List[Dict]:
        """Récupère tous les taux du dernier scraping (même jour que le timestamp max)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        # On récupère le timestamp le plus récent pour MoneyPark
        cursor.execute('''
            SELECT MAX(timestamp) FROM mortgage_rates
            WHERE bank = 'MoneyPark'
        ''')
        last_ts = cursor.fetchone()[0]
        if not last_ts:
            conn.close()
            return []
        # Extraire la date (YYYY-MM-DD) du timestamp max
        last_date = last_ts.split(' ')[0]
        # On récupère tous les taux de ce jour-là
        cursor.execute('''
            SELECT duration, rate, bank, source_url, confidence_score, method, timestamp
            FROM mortgage_rates
            WHERE bank = 'MoneyPark' AND DATE(timestamp) = ?
            ORDER BY duration
        ''', (last_date,))
        rates = cursor.fetchall()
        conn.close()
        return [{
            'duration': r[0],
            'rate': r[1],
            'bank': r[2],
            'source_url': r[3],
            'confidence_score': r[4],
            'method': r[5],
            'timestamp': r[6]
        } for r in rates]
    
    def calculate_mortgage(
        self,
        loan_amount: float,
        duration_years: int,
        interest_rate: float = None,
        use_calculator: bool = False
    ) -> Dict:
        """
        Calcule une simulation hypothécaire
        Peut utiliser le calculateur en ligne ou un calcul local
        """
        if use_calculator and SELENIUM_AVAILABLE:
            return self._use_online_calculator(loan_amount, duration_years)
        else:
            return self._calculate_local(loan_amount, duration_years, interest_rate)
    
    def _use_online_calculator(self, loan_amount: float, duration_years: int) -> Dict:
        """Utilise le calculateur en ligne MoneyPark"""
        url = "https://moneypark.ch/fr/hypotheque/calculateur/"
        
        try:
            if not self.driver:
                self.init_selenium_driver()
            
            self.driver.get(url)
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Remplir le formulaire
            self._fill_calculator_form(loan_amount, duration_years)
            
            # Récupérer les résultats
            result = self._extract_calculator_results()
            
            if result:
                self._save_simulation_to_db({
                    'loan_amount': loan_amount,
                    'duration_years': duration_years,
                    'monthly_payment': result.get('monthly_payment'),
                    'total_cost': result.get('total_cost'),
                    'interest_rate': result.get('interest_rate'),
                    'timestamp': datetime.now()
                }, url, "MoneyPark Calculator")
                return result
        
        except Exception as e:
            logger.error(f"Erreur calculateur en ligne: {e}")
        
        # Fallback au calcul local en cas d'échec
        return self._calculate_local(loan_amount, duration_years)
    
    def _fill_calculator_form(self, loan_amount: float, duration_years: int) -> None:
        """Remplit le formulaire du calculateur MoneyPark avec les bons champs"""
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        import random
        # Valeur du bien
        property_field = WebDriverWait(self.driver, 15).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input[name*='property']"))
        )
        property_field.clear()
        property_field.send_keys(str(int(loan_amount * 1.25)))  # Hypothèse: 80% LTV
        # Fonds propres
        own_funds_field = self.driver.find_element(By.CSS_SELECTOR, "input[name*='own']")
        own_funds_field.clear()
        own_funds_field.send_keys(str(int(loan_amount * 0.25)))
        # Revenus annuels bruts
        income_field = self.driver.find_element(By.CSS_SELECTOR, "input[name*='gross']")
        income_field.clear()
        income_field.send_keys("130000")  # Valeur par défaut raisonnable
        # Taux hypothécaire
        rate_field = self.driver.find_element(By.CSS_SELECTOR, "input[name*='interest']")
        rate_field.clear()
        rate_field.send_keys("5")  # Valeur par défaut
        # Age
        age_field = self.driver.find_element(By.CSS_SELECTOR, "input[name*='age']")
        age_field.clear()
        age_field.send_keys(str(random.randint(30, 55)))
        # Sexe (sélectionne Homme par défaut)
        try:
            homme_radio = self.driver.find_element(By.XPATH, "//input[@type='radio' and @value='1']")
            if not homme_radio.is_selected():
                homme_radio.click()
        except Exception:
            pass
        # Attendre un peu pour que le calcul se fasse
        import time
        time.sleep(2)
    
    def _fill_field_slowly(self, field, value: str) -> None:
        """Remplit un champ lentement pour simuler un comportement humain"""
        field.clear()
        for char in value:
            field.send_keys(char)
            time.sleep(0.1)
    
    def _extract_calculator_results(self) -> Dict:
        """Extrait les résultats du calculateur"""
        try:
            # Essayer de trouver les éléments de résultat
            result_elements = self.driver.find_elements(
                By.XPATH,
                "//*[contains(@class, 'result') or contains(@class, 'calculation')]"
            )
            
            results_text = " ".join([el.text for el in result_elements])
            
            # Patterns pour extraire les valeurs
            monthly_payment = self._extract_value_from_text(
                results_text,
                r'(?:mensualité|payment|monthly)\s*:?\s*([\d\s,]+)\s*(?:chf|franc)'
            )
            
            total_cost = self._extract_value_from_text(
                results_text,
                r'(?:total|coût total|total cost)\s*:?\s*([\d\s,]+)\s*(?:chf|franc)'
            )
            
            interest_rate = self._extract_value_from_text(
                results_text,
                r'(\d+[,.]?\d*)%'
            )
            
            return {
                'monthly_payment': monthly_payment,
                'total_cost': total_cost,
                'interest_rate': interest_rate,
                'calculation_method': 'Online Calculator'
            }
            
        except Exception as e:
            logger.error(f"Erreur extraction résultats: {e}")
            return {}
    
    def _extract_value_from_text(self, text: str, pattern: str) -> Optional[float]:
        """Extrait une valeur numérique depuis un texte avec un regex"""
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            try:
                return float(match.group(1).replace(' ', '').replace(',', ''))
            except ValueError:
                return None
        return None
    
    def _calculate_local(self, loan_amount: float, duration_years: int, interest_rate: float = None) -> Dict:
        """Version améliorée avec gestion d'erreur"""
        try:
            if interest_rate is None:
                rates = self.get_latest_rates()
                interest_rate = next(
                    (r['rate'] for r in rates if str(duration_years) in r['duration']),
                    3.5  # Default fallback
                )
            
            monthly_rate = interest_rate / 1200  # /100 pour % et /12 pour mensuel
            months = duration_years * 12
            monthly_payment = (loan_amount * monthly_rate) / (1 - (1 + monthly_rate)**(-months))
            
            return {
                'loan_amount': loan_amount,
                'duration_years': duration_years,
                'monthly_payment': round(monthly_payment, 2),
                'total_cost': round(monthly_payment * months, 2),
                'interest_rate': interest_rate,
                'calculation_method': 'Local Calculation (Improved)',
                'timestamp': datetime.now()
            }
        except Exception as e:
            logger.error(f"Erreur calcul: {str(e)}")
            return self._get_fallback_simulation(loan_amount, duration_years)
    
    def _save_simulation_to_db(self, simulation_data: Dict, source_url: str, method: str) -> None:
        """Sauvegarde une simulation en base de données avec des valeurs par défaut"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO mortgage_simulations 
            (loan_amount, duration_years, monthly_payment, total_cost, 
            interest_rate, source_url, calculation_method, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            simulation_data['loan_amount'],
            simulation_data['duration_years'],
            simulation_data.get('monthly_payment', 0),  # Valeur par défaut
            simulation_data.get('total_cost', 0),       # Valeur par défaut
            simulation_data.get('interest_rate', 0),    # Valeur par défaut
            source_url,                                 # Maintenant obligatoire
            method,                                     # Maintenant obligatoire
            simulation_data.get('timestamp', datetime.now())
        ))
        
        conn.commit()
        conn.close()
        logger.info("Simulation sauvegardée en base de données")
    
    def get_mortgage_summary(self) -> str:
        """Retourne un résumé professionnel des taux hypothécaires sous forme de tableau Markdown"""
        latest_rates = self.get_latest_rates()
        if not latest_rates:
            return "Aucune donnée de taux hypothécaire disponible."
        summary = "📊 Taux hypothécaires actuels en Suisse :\n\n"
        summary += "Voici la liste complète des taux fixes proposés par MoneyPark selon la durée :\n\n"
        summary += "| Durée | Taux (%) | Banque |\n|-------|----------|--------|\n"
        for rate in latest_rates:
            summary += f"| {rate['duration']} | {rate['rate']} | {rate['bank']} |\n"
        summary += f"\n🔍 Source : {latest_rates[0]['source_url']}"
        summary += f"\n🕒 Dernière mise à jour : {latest_rates[0]['timestamp']}"
        return summary
    
    def get_mortgage_info_summary(self):
        """Alias pour compatibilité avec le chatbot (retourne le résumé des taux)"""
        return self.get_mortgage_summary()
    
    def get_scraping_stats(self) -> Dict:
        """Retourne des statistiques sur les opérations de scraping"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total_attempts,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_attempts,
                AVG(response_time) as avg_response_time
            FROM scraping_logs
        ''')
        stats = cursor.fetchone()
        
        cursor.execute('''
            SELECT method, COUNT(*) 
            FROM scraping_logs 
            GROUP BY method
        ''')
        methods = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_attempts': stats[0],
            'success_rate': (stats[1] / stats[0] * 100) if stats[0] > 0 else 0,
            'avg_response_time': stats[2],
            'methods': dict(methods)
        }

# Exemple d'utilisation
if __name__ == "__main__":
    scraper = SwissMortgageScraper(headless=True)
    
    try:
        print("=== TEST DU SCRAPER UNIFIE ===")
        
        # Test 1: Scraping des taux
        print("\n1. Scraping des taux hypothécaires...")
        rates = scraper.scrape_moneypark_rates(use_selenium=False)  # Essayer sans Selenium d'abord
        
        if not rates or any(r['confidence_score'] < 0.5 for r in rates):
            print("  → Tentative avec Selenium...")
            rates = scraper.scrape_moneypark_rates(use_selenium=True)
        
        for rate in rates:
            print(f"  - {rate['duration']}: {rate['rate']}% (confiance: {rate['confidence_score']})")
        
        # Test 2: Simulation
        print("\n2. Simulation hypothécaire...")
        simulation = scraper.calculate_mortgage(500000, 20, use_calculator=True)
        print(f"  - Mensualité: {simulation['monthly_payment']:,.0f} CHF")
        print(f"  - Méthode: {simulation['calculation_method']}")
        
        # Test 3: Résumé
        print("\n3. Résumé des informations:")
        print(scraper.get_mortgage_summary())
        
        # Test 4: Statistiques
        print("\n4. Statistiques de scraping:")
        stats = scraper.get_scraping_stats()
        print(f"  - Taux de succès: {stats['success_rate']:.1f}%")
        print(f"  - Méthodes utilisées: {stats['methods']}")
        
    finally:
        scraper.close_selenium()