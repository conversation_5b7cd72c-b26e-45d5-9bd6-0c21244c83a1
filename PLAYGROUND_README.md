# 🧪 Playground de Test des Modèles LLM

Ce dossier contient les outils pour tester et comparer les modèles LLM disponibles dans votre projet.

## 📋 Modèles Supportés

- **Mistral Tiny** (`mistral/mistral-tiny`)
- **Gemini 2.5 Pro** (`gemini/gemini-2.5-pro`) 
- **Gemini 1.5 Pro Latest** (`gemini/gemini-1.5-pro-latest`) ⭐ **NOUVEAU**

## 🚀 Utilisation Rapide

### 1. Test Simple - Gemini 1.5 Pro Latest

```bash
python test_gemini_1_5_pro.py
```

Ce script teste rapidement le nouveau modèle Gemini 1.5 Pro Latest et compare les 3 modèles.

### 2. Playground Complet - Comparaison Détaillée

```bash
python playground_models.py
```

Ce script lance une batterie de tests complète avec :
- ⏱️ Mesure des temps de réponse
- 📊 Analyse de la qualité des réponses
- 🎯 Score de pertinence basé sur des mots-clés
- 📈 Statistiques comparatives

## 📊 Exemple de Sortie

```
🚀 PLAYGROUND DE TEST DES MODÈLES LLM
================================================================================
📅 Date: 2024-01-15 14:30:25
🔧 Modèles testés: mistral, gemini-2.5-pro, gemini-1.5-pro-latest
❓ Questions de test: 4

================================================================================
📋 CATÉGORIE: Taux hypothécaires
================================================================================

🤖 Test mistral
📝 Question: Quels sont les facteurs qui influencent les taux hypothécaires en Suisse ?
⏱️  Temps de réponse: 2.34s
📊 Nombre de mots: 156
🎯 Score mots-clés: 75.0% (3/4)
🔍 Mots-clés trouvés: banque nationale, inflation, marché

🤖 Test gemini-2.5-pro
📝 Question: Quels sont les facteurs qui influencent les taux hypothécaires en Suisse ?
⏱️  Temps de réponse: 1.89s
📊 Nombre de mots: 203
🎯 Score mots-clés: 100.0% (4/4)
🔍 Mots-clés trouvés: banque nationale, inflation, marché, risque

🤖 Test gemini-1.5-pro-latest
📝 Question: Quels sont les facteurs qui influencent les taux hypothécaires en Suisse ?
⏱️  Temps de réponse: 1.67s
📊 Nombre de mots: 187
🎯 Score mots-clés: 100.0% (4/4)
🔍 Mots-clés trouvés: banque nationale, inflation, marché, risque

================================================================================
📊 RÉSUMÉ COMPARATIF
================================================================================

Modèle               Succès   Temps    Mots     Score   
------------------------------------------------------------
mistral              100.0%   2.45s    142      68.8%
gemini-2.5-pro       100.0%   1.92s    198      93.8%
gemini-1.5-pro-latest 100.0%   1.73s    185      96.9%

🏆 RECOMMANDATIONS:
⚡ Plus rapide: gemini-1.5-pro-latest (1.73s)
🎯 Plus pertinent: gemini-1.5-pro-latest (96.9%)
📝 Plus détaillé: gemini-2.5-pro (198 mots)
```

## 🔧 Configuration

### Fichier .env

Assurez-vous d'avoir un fichier `.env` avec vos clés API :

```env
# Modèle par défaut
LITELLM_MODEL=gemini/gemini-2.5-pro

# Clés API (ajoutez celles dont vous avez besoin)
GOOGLE_API_KEY=your_google_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### Interface Web

Le nouveau modèle **Gemini 1.5 Pro Latest** est maintenant disponible dans le dropdown de l'interface web :

1. Ouvrez votre chatbot web
2. Cliquez sur le dropdown des modèles
3. Sélectionnez "Gemini 1.5 Pro Latest" 🟢

## 📝 Questions de Test

Le playground teste 4 catégories de questions :

1. **Taux hypothécaires** - Facteurs d'influence
2. **Simulation prêt** - Calculs de mensualités  
3. **Réglementation** - Exigences légales
4. **Types d'hypothèques** - Différences entre produits

## 🎯 Métriques Évaluées

- **Temps de réponse** - Vitesse du modèle
- **Nombre de mots** - Détail des réponses
- **Score de pertinence** - Présence de mots-clés attendus
- **Taux de succès** - Fiabilité du modèle

## 🔄 Personnalisation

### Ajouter de Nouveaux Modèles

Modifiez `MODELS_TO_TEST` dans `playground_models.py` :

```python
MODELS_TO_TEST = {
    "mistral": "mistral/mistral-tiny",
    "gemini-2.5-pro": "gemini/gemini-2.5-pro", 
    "gemini-1.5-pro-latest": "gemini/gemini-1.5-pro-latest",
    "gpt-4": "gpt-4",  # Nouveau modèle
}
```

### Ajouter de Nouvelles Questions

Modifiez `TEST_QUESTIONS` dans `playground_models.py` :

```python
{
    "category": "Nouvelle catégorie",
    "question": "Votre question ici ?",
    "expected_keywords": ["mot1", "mot2", "mot3"]
}
```

## 🚨 Dépannage

### Erreur de clé API
```
❌ Erreur: API key not found
```
➡️ Vérifiez votre fichier `.env` et les clés API

### Erreur de rate limit
```
❌ Erreur: Rate limit exceeded
```
➡️ Le script inclut des pauses automatiques, attendez quelques minutes

### Modèle non trouvé
```
❌ Erreur: Model not found
```
➡️ Vérifiez que le nom du modèle est correct dans LiteLLM

## 📚 Ressources

- [Documentation LiteLLM](https://docs.litellm.ai/)
- [Modèles Gemini](https://ai.google.dev/models/gemini)
- [Modèles Mistral](https://docs.mistral.ai/models/)

---

**💡 Conseil :** Lancez d'abord `test_gemini_1_5_pro.py` pour un test rapide, puis `playground_models.py` pour une analyse complète !
