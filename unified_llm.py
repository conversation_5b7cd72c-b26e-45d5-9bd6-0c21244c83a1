import os
from dotenv import load_dotenv
import litellm

# Charger les variables d'environnement du .env
load_dotenv()

# Récupérer le modèle à utiliser à partir du .env
model = os.getenv("LITELLM_MODEL")

def ask_llm(prompt_or_messages, model_name=None):
    """Appelle le LLM avec le modèle configuré, en gérant l'historique."""
    current_model = model_name or model

    if not current_model:
        raise ValueError("Le modèle LLM n'est pas configuré. Vérifiez la variable LITELLM_MODEL dans votre fichier .env")

    if isinstance(prompt_or_messages, list):
        messages = prompt_or_messages
    else:
        messages = [{"role": "user", "content": prompt_or_messages}]

    response = litellm.completion(
        model=current_model,
        messages=messages
    )
    return response['choices'][0]['message']['content']

def ask_llm_stream(prompt_or_messages, model_name=None):
    """A<PERSON>le le LLM en mode streaming, yield chaque token/fragment généré."""
    current_model = model_name or model
    if not current_model:
        raise ValueError("Le modèle LLM n'est pas configuré. Vérifiez la variable LITELLM_MODEL dans votre fichier .env")
    if isinstance(prompt_or_messages, list):
        messages = prompt_or_messages
    else:
        messages = [{"role": "user", "content": prompt_or_messages}]
    response = litellm.completion(
        model=current_model,
        messages=messages,
        stream=True
    )
    for chunk in response:
        # Pour OpenAI/Gemini, le texte est dans chunk.choices[0].delta.content
        delta = getattr(chunk.choices[0], 'delta', None)
        if delta and getattr(delta, 'content', None):
            yield delta.content

if __name__ == "__main__":
    question = "Explique le fonctionnement d'un prêt hypothécaire en Suisse."
    try:
        print(ask_llm(question))
    except Exception as e:
        print(f"Une erreur est survenue : {e}") 