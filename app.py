from flask import Flask, render_template, request, jsonify, Response
from chat_rag import ask_bot, ask_bot_stream
import socket
import json
import re

app = Flask(__name__)

@app.route("/")
def index():
    return render_template("chat.html")

@app.route("/chat", methods=["POST"])
def chat():
    data = request.get_json()
    user_message = data.get("message", "")
    if not user_message.strip():
        return jsonify({"response": "Merci de poser une question sur l'immobilier en Suisse."})
    try:
        bot_response = ask_bot(user_message)
    except Exception as e:
        bot_response = f"Erreur lors de la génération de la réponse : {e}"
    return jsonify({"response": bot_response})

@app.route("/chat/stream", methods=["POST"])
def chat_stream():
    data = request.get_json()
    messages = data.get("messages", [])
    model = data.get("model")
    if not messages or not isinstance(messages, list):
        return jsonify({"response": "<PERSON>rc<PERSON> de poser une question sur l'immobilier en Suisse."})
    user_message = None
    for msg in reversed(messages):
        if msg.get("role") == "user":
            user_message = msg.get("content", "")
            break
    if not user_message or not user_message.strip():
        return jsonify({"response": "Merci de poser une question sur l'immobilier en Suisse."})

    def generate():
        try:
            buffer = ""
            for token in ask_bot_stream(user_message, messages, model):
                if token:
                    buffer += token
                    yield f"data: {{\"content\": {json.dumps(token)}, \"done\": false}}\n\n"
            yield f"data: {{\"content\": \"\", \"done\": true}}\n\n"
        except Exception as e:
            error_message = f"Erreur lors de la génération de la réponse : {e}"
            yield f"data: {{\"content\": {json.dumps(error_message)}, \"done\": true}}\n\n"
    return Response(generate(), mimetype='text/event-stream')


def find_free_port(start_port=8080):
    """Trouve un port libre à partir du port de départ"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

if __name__ == "__main__":
    # Essayer de trouver un port libre
    port = find_free_port(8080)
    if port is None:
        print("❌ Impossible de trouver un port libre")
        exit(1)
    
    print(f"🚀 Démarrage du chatbot sur http://localhost:{port}")
    print("📱 Ouvrez votre navigateur et allez à l'adresse ci-dessus")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
    
    try:
        app.run(host="0.0.0.0", port=port, debug=True)
    except OSError as e:
        print(f"❌ Erreur lors du démarrage du serveur: {e}")
        print("💡 Essayez de fermer d'autres applications qui utilisent le port")
    except KeyboardInterrupt:
        print("\n👋 Serveur arrêté par l'utilisateur") 