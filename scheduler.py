#!/usr/bin/env python3
"""
Service de planification pour les mises à jour automatiques
Met à jour les taux hypothécaires toutes les 24h avec l'agent IA avancé
"""

import schedule
import time
import logging
from datetime import datetime, timedelta
from advanced_web_agent import AdvancedMortgageAgent
from extract_pdf import PDFExtractor
import sqlite3
import json
import os

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedMortgageDataScheduler:
    def __init__(self, db_path="mortgage_data.db"):
        """Initialise le planificateur avancé"""
        self.db_path = db_path
        self.agent = AdvancedMortgageAgent(db_path)
        self.pdf_extractor = PDFExtractor()
        self.last_update = None
        
    def update_mortgage_rates(self):
        """Met à jour les taux hypothécaires avec l'agent IA avancé"""
        try:
            logger.info("Début de la mise à jour des taux hypothécaires (Agent IA)...")
            
            # Utiliser l'agent avancé pour scraper les taux
            rates = self.agent.scrape_moneypark_rates()
            
            if rates:
                self.last_update = datetime.now()
                logger.info(f"Mise à jour réussie: {len(rates)} taux récupérés")
                
                # Sauvegarder le timestamp de la dernière mise à jour
                self.save_last_update_timestamp("SUCCESS", f"Agent IA: {len(rates)} taux")
                
                return True
            else:
                logger.warning("Aucun taux récupéré lors de la mise à jour")
                self.save_last_update_timestamp("WARNING", "Aucun taux récupéré")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour: {e}")
            self.save_last_update_timestamp("ERROR", str(e))
            return False
    
    def update_pdf_database(self):
        """Met à jour la base de données PDF"""
        try:
            logger.info("Début de la mise à jour de la base PDF...")
            
            # Extraire les PDFs du dossier PDFs/
            pdf_folder = "PDFs"
            if os.path.exists(pdf_folder):
                pdf_files = [f for f in os.listdir(pdf_folder) if f.endswith('.pdf')]
                
                if pdf_files:
                    extracted_count = 0
                    for pdf_file in pdf_files:
                        pdf_path = os.path.join(pdf_folder, pdf_file)
                        try:
                            success = self.pdf_extractor.extract_and_store(pdf_path)
                            if success:
                                extracted_count += 1
                                logger.info(f"PDF extrait avec succès: {pdf_file}")
                            else:
                                logger.warning(f"Échec extraction PDF: {pdf_file}")
                        except Exception as e:
                            logger.error(f"Erreur lors de l'extraction de {pdf_file}: {e}")
                    
                    logger.info(f"Mise à jour PDF réussie: {extracted_count}/{len(pdf_files)} fichiers")
                    return True
                else:
                    logger.info("Aucun fichier PDF trouvé dans le dossier PDFs/")
                    return True
            else:
                logger.warning("Dossier PDFs/ non trouvé")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour PDF: {e}")
            return False
    
    def test_all_systems(self):
        """Test complet de tous les systèmes (PDFs + liens web)"""
        try:
            logger.info("=== DÉBUT DU TEST COMPLET DES SYSTÈMES ===")
            
            results = {
                'timestamp': datetime.now(),
                'web_scraping': False,
                'pdf_extraction': False,
                'calculator': False,
                'statistics': {}
            }
            
            # Test 1: Scraping web
            print("\n1. Test scraping web (Agent IA)...")
            rates = self.agent.scrape_moneypark_rates()
            if rates:
                results['web_scraping'] = True
                print(f"✅ Scraping web réussi: {len(rates)} taux")
                for rate in rates:
                    print(f"  - {rate['duration']}: {rate['rate']}% (confiance: {rate.get('confidence_score', 0):.2f})")
            else:
                print("❌ Échec scraping web")
            
            # Test 2: Extraction PDF
            print("\n2. Test extraction PDF...")
            pdf_success = self.update_pdf_database()
            results['pdf_extraction'] = pdf_success
            if pdf_success:
                print("✅ Extraction PDF réussie")
            else:
                print("❌ Échec extraction PDF")
            
            # Test 3: Calculateur
            print("\n3. Test calculateur MoneyPark...")
            simulation = self.agent.use_moneypark_calculator(500000, 20)
            if simulation:
                results['calculator'] = True
                print("✅ Calculateur fonctionnel")
                print(f"  - Mensualité: {simulation.get('monthly_payment', 'N/A'):,.0f} CHF")
                print(f"  - Méthode: {simulation.get('calculation_method', 'N/A')}")
            else:
                print("❌ Échec calculateur")
            
            # Test 4: Statistiques
            print("\n4. Statistiques système...")
            stats = self.agent.get_scraping_statistics()
            results['statistics'] = stats
            print(f"✅ Statistiques récupérées:")
            print(f"  - Taux de succès: {stats['success_rate']:.1f}%")
            print(f"  - Temps de réponse moyen: {stats['avg_response_time']:.2f}s")
            
            # Sauvegarder les résultats
            self.save_test_results(results)
            
            logger.info("=== FIN DU TEST COMPLET DES SYSTÈMES ===")
            
            return results
            
        except Exception as e:
            logger.error(f"Erreur lors du test complet: {e}")
            return None
    
    def save_last_update_timestamp(self, status, notes):
        """Sauvegarde le timestamp de la dernière mise à jour"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Créer la table si elle n'existe pas
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS update_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    last_update DATETIME,
                    status TEXT,
                    notes TEXT
                )
            ''')
            
            # Insérer ou mettre à jour le timestamp
            cursor.execute('''
                INSERT OR REPLACE INTO update_log (id, last_update, status, notes)
                VALUES (1, ?, ?, ?)
            ''', (self.last_update, status, notes))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde du timestamp: {e}")
    
    def save_test_results(self, results):
        """Sauvegarde les résultats des tests"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Créer la table si elle n'existe pas
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME,
                    web_scraping BOOLEAN,
                    pdf_extraction BOOLEAN,
                    calculator BOOLEAN,
                    statistics TEXT
                )
            ''')
            
            # Insérer les résultats
            cursor.execute('''
                INSERT INTO test_results (timestamp, web_scraping, pdf_extraction, calculator, statistics)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                results['timestamp'],
                results['web_scraping'],
                results['pdf_extraction'],
                results['calculator'],
                json.dumps(results['statistics'])
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des résultats: {e}")
    
    def get_last_update_info(self):
        """Récupère les informations de la dernière mise à jour"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT last_update, status, notes 
                FROM update_log 
                WHERE id = 1
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'last_update': result[0],
                    'status': result[1],
                    'notes': result[2]
                }
            else:
                return None
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des infos de mise à jour: {e}")
            return None
    
    def should_update(self):
        """Vérifie si une mise à jour est nécessaire (plus de 24h depuis la dernière)"""
        last_update_info = self.get_last_update_info()
        
        if not last_update_info:
            logger.info("Aucune mise à jour précédente trouvée, mise à jour nécessaire")
            return True
        
        try:
            last_update = datetime.fromisoformat(last_update_info['last_update'])
            time_since_update = datetime.now() - last_update
            
            # Mise à jour si plus de 24h
            if time_since_update > timedelta(hours=24):
                logger.info(f"Mise à jour nécessaire (dernière: {last_update})")
                return True
            else:
                logger.info(f"Mise à jour non nécessaire (dernière: {last_update})")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de la vérification de la mise à jour: {e}")
            return True
    
    def start_scheduler(self):
        """Démarre le planificateur avancé"""
        logger.info("Démarrage du planificateur avancé de mise à jour...")
        
        # Planifier les mises à jour
        schedule.every().day.at("08:00").do(self.update_mortgage_rates)
        schedule.every().day.at("09:00").do(self.update_pdf_database)
        
        # Vérifier si une mise à jour est nécessaire au démarrage
        if self.should_update():
            logger.info("Mise à jour initiale nécessaire...")
            self.update_mortgage_rates()
            self.update_pdf_database()
        
        logger.info("Planificateur avancé démarré - Mises à jour programmées")
        logger.info("- Taux hypothécaires: 8h00")
        logger.info("- Base PDF: 9h00")
        
        # Boucle principale du planificateur
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # Vérifier toutes les minutes
            except KeyboardInterrupt:
                logger.info("Arrêt du planificateur...")
                break
            except Exception as e:
                logger.error(f"Erreur dans la boucle du planificateur: {e}")
                time.sleep(300)  # Attendre 5 minutes en cas d'erreur
    
    def manual_update(self):
        """Force une mise à jour manuelle"""
        logger.info("Mise à jour manuelle demandée...")
        success_rates = self.update_mortgage_rates()
        success_pdf = self.update_pdf_database()
        return success_rates and success_pdf
    
    def get_status(self):
        """Retourne le statut complet du système"""
        last_update_info = self.get_last_update_info()
        latest_rates = self.agent.get_latest_rates()
        stats = self.agent.get_scraping_statistics()
        
        # Vérifier les PDFs
        pdf_folder = "PDFs"
        pdf_count = 0
        if os.path.exists(pdf_folder):
            pdf_count = len([f for f in os.listdir(pdf_folder) if f.endswith('.pdf')])
        
        status = {
            'scheduler_running': True,
            'last_update': last_update_info['last_update'] if last_update_info else None,
            'update_status': last_update_info['status'] if last_update_info else 'UNKNOWN',
            'rates_count': len(latest_rates) if latest_rates else 0,
            'pdf_count': pdf_count,
            'scraping_success_rate': stats.get('success_rate', 0),
            'avg_response_time': stats.get('avg_response_time', 0),
            'next_update': '08:00 demain' if last_update_info else 'Immédiat'
        }
        
        return status

# Service de notification (optionnel)
class NotificationService:
    def __init__(self):
        self.notifications = []
    
    def add_notification(self, message, level="INFO"):
        """Ajoute une notification"""
        notification = {
            'timestamp': datetime.now(),
            'message': message,
            'level': level
        }
        self.notifications.append(notification)
        
        # Garder seulement les 100 dernières notifications
        if len(self.notifications) > 100:
            self.notifications = self.notifications[-100:]
    
    def get_recent_notifications(self, count=10):
        """Récupère les notifications récentes"""
        return self.notifications[-count:] if self.notifications else []

# Test du planificateur avancé
if __name__ == "__main__":
    scheduler = AdvancedMortgageDataScheduler()
    
    print("=== Test du planificateur avancé de taux hypothécaires ===")
    
    # Test complet de tous les systèmes
    print("\n1. Test complet de tous les systèmes...")
    results = scheduler.test_all_systems()
    
    if results:
        print(f"\nRésultats du test complet:")
        print(f"  - Scraping web: {'✅' if results['web_scraping'] else '❌'}")
        print(f"  - Extraction PDF: {'✅' if results['pdf_extraction'] else '❌'}")
        print(f"  - Calculateur: {'✅' if results['calculator'] else '❌'}")
    
    # Test statut
    print("\n2. Statut du système...")
    status = scheduler.get_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    # Test mise à jour manuelle
    print("\n3. Test mise à jour manuelle...")
    success = scheduler.manual_update()
    print(f"Résultat: {'SUCCES' if success else 'ECHEC'}")
    
    # Démarrer le planificateur (optionnel)
    print("\n4. Démarrage du planificateur (Ctrl+C pour arrêter)...")
    try:
        scheduler.start_scheduler()
    except KeyboardInterrupt:
        print("\nPlanificateur arrêté par l'utilisateur")
