# 🤖 Assistant Imm<PERSON><PERSON><PERSON> - Chatbot RAG

## 🆕 Mises à jour récentes

- Sauvegarde de l'historique du chat dans le localStorage du navigateur
- Rendu Markdown pour les réponses du bot (gras, listes, titres, etc.)
- Utilisation de Web Components et Shadow DOM pour un chat encapsulé et un style isolé
- Découpage optimisé des documents PDF (180 mots par chunk, ajustable)
- Utilisation de 5 chunks pour enrichir le contexte transmis à l'IA
- Streaming des réponses pour un affichage progressif
- Prise en charge de plusieurs modèles LLM (Mistral, GPT, Claude, Gemini...)
- Prompt système qui force l'usage du Markdown dans les réponses IA

Un assistant intelligent spécialisé dans l'immobilier suisse, utilisant l'IA pour répondre aux questions basées sur vos documents PDF.

## 🏗️ Architecture Microservices

Le projet utilise une architecture microservices avec séparation des responsabilités :

-   **main.py** : Orchestrateur principal qui coordonne les services
-   **create_db.py** : Service de création et gestion de la base vectorielle
-   **chat_rag.py** : Service de chatbot IA avec recherche RAG
-   **extract_pdf.py** : Extraction de texte depuis les PDFs
-   **web_scraper.py** : Scraping des taux hypothécaires actuels
-   **app.py** : Serveur web Flask pour l'interface utilisateur
-   **advanced_web_agent.py** : Agent web avancé pour automatisation ou scraping
-   **scheduler.py** : Planification de tâches (ex: mises à jour périodiques)
-   **demo_comparison.py** : Script de comparaison de résultats ou de modèles
-   **test_advanced_agent.py** : Tests unitaires pour l'agent avancé
-   **test_scraping.py** : Tests unitaires pour le scraping

## 🚀 Installation et Utilisation

### Prérequis

-   Docker installé sur votre machine
-   Fichiers PDF sur l'immobilier suisse dans le dossier `PDFs/`

### Structure du projet

```
chatbot-immobilier/
├── PDFs/                    # Placez vos PDFs ici
├── chroma_db/              # Base de données vectorielle (créée automatiquement)
├── main.py                 # Orchestrateur principal
├── create_db.py            # Service de création et gestion de la base vectorielle
├── chat_rag.py             # Service de chatbot IA avec recherche RAG
├── extract_pdf.py          # Extraction de texte depuis les PDFs
├── web_scraper.py          # Scraping des taux hypothécaires actuels
├── app.py                  # Serveur web Flask pour l'interface utilisateur
├── advanced_web_agent.py   # Agent web avancé pour automatisation ou scraping
├── scheduler.py            # Planification de tâches (ex: mises à jour périodiques)
├── demo_comparison.py      # Script de comparaison de résultats ou de modèles
├── test_advanced_agent.py  # Tests unitaires pour l'agent avancé
├── test_scraping.py        # Tests unitaires pour le scraping
├── mortgage_data.db        # Base de données d'exemple (prêts hypothécaires)
├── demo_new.db             # Base de données d'exemple (nouvelle version)
├── demo_old.db             # Base de données d'exemple (ancienne version)
├── pdf_status.json         # Statut d'indexation des PDFs
├── selenium_moneypark_dump.html # Dump HTML pour tests de scraping
├── static/                 # Fichiers statiques (JS, CSS) pour l'interface web
├── templates/              # Templates HTML pour Flask
├── requirements.txt        # Dépendances Python
├── Dockerfile              # Configuration Docker
├── .gitignore              # Fichiers/dossiers ignorés par git
└── README.md               # Ce fichier
```

## 🎯 Utilisation Simple

### 1. Préparer vos documents

Placez vos fichiers PDF sur l'immobilier suisse dans le dossier `PDFs/`

### 2. Construire l'image Docker (première fois uniquement)

```powershell
docker build --no-cache -t chatbot .
```

### 3. Lancer le système complet (avec accès web)

```powershell
docker run -e FORCE_REINDEX=1 -p 8080:8080 -v "C:/chatbot immobilier/chatbot-immobilier/PDFs:/app/PDFs" -v "C:/chatbot immobilier/chatbot-immobilier/chroma_db:/app/chroma_db" chatbot
```

#### Sous Linux/MacOS :

```bash
docker run -e FORCE_REINDEX=1 -p 8080:8080 -v "$(pwd)/PDFs:/app/PDFs" -v "$(pwd)/chroma_db:/app/chroma_db" chatbot
```

**Le système va automatiquement :**

-   ✅ Vérifier vos PDFs
-   ✅ Lancer le service de création de base de données
-   ✅ Lancer le service de chatbot
-   ✅ Lancer le service de scraping des taux hypothécaires actuels
-   ✅ Se mettre en mode interactif

### 4. Accéder à l'interface web

Ouvrez votre navigateur à l'adresse : [http://localhost:8080](http://localhost:8080)

> **Remarque :** Si le port 8080 est déjà utilisé, l'application tentera d'utiliser un port libre entre 8080 et 8179. Consultez les logs du conteneur pour connaître le port exact.

### 5. Poser vos questions

Exemples de questions :

-   "Quels sont les frais à prévoir lors de l'achat d'un bien immobilier en Suisse ?"
-   "Quelles sont les tendances du marché immobilier à Lausanne ?"
-   "Comment fonctionne l'hypothèque en Suisse ?"
-   "Quelles sont les taxes immobilières en Suisse ?"
-   Combien coûterait un prêt de 1 million CHF sur 30 ans ?
-   Quels sont les taux hypothécaires actuels en Suisse ?
-   Quels sont les frais liés à l'achat immobilier en Suisse ?
-   Comment fonctionne le processus d'achat immobilier en Suisse ?

## 🔧 Utilisation Avancée (Microservices)

### Lancer les services individuellement :

```powershell
# Service de base de données uniquement
docker run -it --rm -v "${PWD}\PDFs:/app/PDFs" -v "${PWD}\chroma_db:/app/chroma_db" mon-rag-bot python create_db.py

# Service de chatbot uniquement
docker run -it --rm -v "${PWD}\PDFs:/app/PDFs" -v "${PWD}\chroma_db:/app/chroma_db" mon-rag-bot python chat_rag.py

# Service de scraping des taux hypothécaires actuels uniquement
docker run -it --rm -v "${PWD}\PDFs:/app/PDFs" -v "${PWD}\chroma_db:/app/chroma_db" mon-rag-bot python web_scraper.py

# Orchestrateur principal (recommandé)
docker run -it --rm -v "${PWD}\PDFs:/app/PDFs" -v "${PWD}\chroma_db:/app/chroma_db" mon-rag-bot python main.py
```

### Pour quitter le chatbot :
Tapez `exit` ou `quit` dans le terminal.

## 📋 Fonctionnalités

- **🔍 Recherche intelligente** : Trouve les informations pertinentes dans vos PDFs
- **🤖 IA spécialisée** : Réponses précises sur l'immobilier suisse
- **📚 Base de données automatique** : Met à jour automatiquement quand vous ajoutez/modifiez des PDFs
- **🚫 Filtrage de domaine** : Ne répond qu'aux questions sur l'immobilier suisse
- **💾 Persistance** : Garde en mémoire vos documents entre les sessions
- **🏗️ Architecture modulaire** : Services séparés pour maintenance et évolutivité
- **📝 Historique du chat** : L'historique des conversations est sauvegardé dans le localStorage du navigateur
- **🖋️ Rendu Markdown** : Les réponses du bot sont formatées (gras, listes, titres, etc.) grâce à Markdown
- **🧩 Web Components & Shadow DOM** : Le chat est encapsulé dans un composant isolé pour éviter les conflits CSS
- **📏 Découpage optimisé des documents** : Les PDF sont découpés en chunks de 180 mots (ajustable)
- **📚 Contexte enrichi** : Jusqu'à 5 chunks sont utilisés pour chaque question pour améliorer la pertinence
- **⏳ Streaming des réponses** : Les réponses du bot sont envoyées en streaming pour un affichage progressif
- **🔄 Prêt pour plusieurs LLM** : Le backend peut basculer dynamiquement entre plusieurs modèles IA (Mistral, GPT, Claude, Gemini...)
- **🛡️ Prompt système avancé** : Le prompt force l'usage du Markdown dans les réponses IA pour un rendu enrichi

## 🛠️ Dépannage

### Le chatbot ne répond pas ?
1. Vérifiez que vous avez des PDFs dans le dossier `PDFs/`
2. Vérifiez que Docker est bien installé et fonctionne
3. Vérifiez que l'image Docker `mon-rag-bot` existe
4. Reconstruisez l'image : `docker build -t mon-rag-bot .`

### Erreur de chemin ?
Utilisez le chemin absolu complet dans la commande Docker.

### Le chatbot répond "aucune information trouvée" ?
- Vérifiez que vos PDFs contiennent bien des informations sur l'immobilier suisse
- Essayez de reformuler votre question avec des mots-clés plus généraux

### Problème avec un service spécifique ?
Vous pouvez lancer les services individuellement pour diagnostiquer le problème.

### Erreur d'encodage Unicode ?
- Le système utilise maintenant du texte simple au lieu d'emojis
- Compatible avec tous les terminaux Windows

## 📝 Étapes de Démarrage Rapide

1. **Ouvrir PowerShell** dans le dossier du projet
2. **Construire l'image** : `docker build --no-cache -t chatbot .`
3. **Lancer le système** : `docker run -e FORCE_REINDEX=1 -p 8080:8080 -v "C:/chatbot immobilier/chatbot-immobilier/PDFs:/app/PDFs" -v "C:/chatbot immobilier/chatbot-immobilier/chroma_db:/app/chroma_db" chatbot`
4. **Attendre le démarrage** (téléchargement des modèles IA)
5. **Ouvrir [http://localhost:8080](http://localhost:8080) dans votre navigateur**
6. **Poser des questions** sur l'immobilier suisse

## 🔄 Mise à Jour

### Ajouter de nouveaux PDFs :
1. Placez les nouveaux PDFs dans le dossier `PDFs/`
2. Relancez le système - il détectera automatiquement les nouveaux fichiers
3. La base de données sera mise à jour automatiquement

### Modifier un PDF existant :
1. Remplacez le fichier PDF avec le meme nom dans le dossier `PDFs/`
2. Relancez le système - il détectera les modifications
3. La base de données sera mise à jour automatiquement

## 📦 Dépendances et Docker

- Toutes les dépendances Python sont installées automatiquement dans l'image Docker, y compris :
  - torch (CPU-only)
  - sentence-transformers
  - PyMuPDF
  - selenium, webdriver-manager
  - Flask, chromadb, numpy, lxml, requests, beautifulsoup4, schedule, huggingface-hub
- Le fichier `requirements.txt` est utilisé pour le cache Docker, mais l'installation se fait explicitement dans le Dockerfile pour optimiser la taille et la rapidité de build.
- **Aucune installation manuelle de dépendances n'est requise côté utilisateur.**

## 📞 Support

Pour toute question ou problème, contactez moi sur whatsApp +21629867680.

---

**Note :** Le chatbot est spécialisé dans l'immobilier suisse. Il ne répondra qu'aux questions liées à ce domaine.

---

### 🆕 Mise à jour Docker et accès web
- Le port 8080 est exposé par défaut pour l'accès à l'interface web.
- Si le port 8080 est déjà utilisé, l'application tentera d'en trouver un autre libre (entre 8080 et 8179).
- Pensez à utiliser l'option `-p 8080:8080` dans vos commandes Docker pour accéder à l'interface web depuis votre navigateur.

## 🚀 Lancement Docker avec réindexation automatique

Pour garantir que la base documentaire est toujours à jour dans Docker, lancez le conteneur avec la variable d'environnement `FORCE_REINDEX=1` :

```sh
# Sous PowerShell (Windows)
docker run -e FORCE_REINDEX=1 -p 8080:8080 -v "C:/chatbot immobilier/chatbot-immobilier/PDFs:/app/PDFs" -v "C:/chatbot immobilier/chatbot-immobilier/chroma_db:/app/chroma_db" chatbot

# Sous Linux/MacOS
docker run -e FORCE_REINDEX=1 -p 8080:8080 -v "$(pwd)/PDFs:/app/PDFs" -v "$(pwd)/chroma_db:/app/chroma_db" chatbot
docker run -e FORCE_REINDEX=1 -p 8080:8080 -v "PDFs:/app/PDFs" -v "chroma_db:/app/chroma_db" chatbot
````

À chaque démarrage, la base vectorielle sera reconstruite à partir des PDFs présents.

## 🔄 Changer de modèle d'IA dynamiquement avec LiteLLM

Le projet intègre [LiteLLM](https://github.com/BerriAI/litellm) pour permettre de changer de modèle d'IA (OpenAI, Mistral, Gemini, etc.) sans modifier le code.

1. Installez les dépendances :
   ```bash
   pip install -r requirements.txt
   ```
2. Créez un fichier `.env` à la racine du projet :
   ```env
   LITELLM_MODEL=gpt-4
   LITELLM_API_KEY=sk-xxxxxxx
   # Ajoutez d'autres clés si besoin (MISTRAL_API_KEY, ANTHROPIC_API_KEY, ...)
   ```
3. Utilisez la fonction `ask_llm()` du script `unified_llm.py` pour interroger le modèle choisi.
4. Pour changer de modèle, modifiez simplement la variable `LITELLM_MODEL` dans `.env` (ex: `mistral-7b`, `gemini-pro`, etc.).

---
