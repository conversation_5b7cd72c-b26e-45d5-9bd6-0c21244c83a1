import chromadb
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import requests
import sys
import traceback
import os
import hashlib
import json
from sentence_transformers import SentenceTransformer
import fitz  # PyMuPDF
from web_scraper import SwissMortgageScraper
from dotenv import load_dotenv
from unified_llm import ask_llm, ask_llm_stream  # Ajouté pour l'appel universel LLM + streaming

# ==== Chargement des variables d'environnement ====
load_dotenv()

# ==== Configuration API OpenRouter ====
API_KEY = os.getenv("OPENROUTER_API_KEY")
if not API_KEY and os.getenv("LITELLM_MODEL", "").startswith("openrouter/"):
    raise ValueError("La clé API OpenRouter n'est pas configurée. Vérifiez votre fichier .env")

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "HTTP-Referer": "https://openrouter.ai",
    "Content-Type": "application/json"
}

# ==== Connexion à Chroma DB ====
embedding_function = SentenceTransformerEmbeddingFunction(model_name="all-MiniLM-L6-v2")
client = chromadb.PersistentClient(path="./chroma_db")
collection = client.get_or_create_collection(name="immobilier_suisse")

# ==== Service de scraping des taux hypothécaires ====
mortgage_scraper = SwissMortgageScraper()

# ==== Paramètres tokens ====
MAX_QUESTION_TOKENS = 200
MAX_RESPONSE_TOKENS = 1000

# ==== Fonctions pour la création automatique de la base ====
def extraire_texte_depuis_pdf(chemin_pdf):
    if not os.path.isfile(chemin_pdf):
        print(f"ERREUR: Fichier PDF introuvable : {chemin_pdf}")
        return None
    doc = fitz.open(chemin_pdf)
    texte = ""
    for page in doc:
        texte += page.get_text()
    doc.close()
    return texte

def decouper_texte(texte, max_mots=180):
    mots = texte.split()
    chunks = []
    for i in range(0, len(mots), max_mots):
        chunk = " ".join(mots[i:i + max_mots])
        chunks.append(chunk)
    return chunks

def supprimer_chunks_pdf(collection, nom_pdf):
    ids_a_supprimer = []
    results = collection.get()
    for doc_id in results["ids"]:
        if doc_id.startswith(nom_pdf):
            ids_a_supprimer.append(doc_id)
    if ids_a_supprimer:
        collection.delete(ids=ids_a_supprimer)
        print(f"{len(ids_a_supprimer)} anciens chunks supprimés pour : {nom_pdf}")

def hash_pdf(chemin_pdf):
    sha256 = hashlib.sha256()
    with open(chemin_pdf, 'rb') as f:
        while True:
            data = f.read(65536)
            if not data:
                break
            sha256.update(data)
    return sha256.hexdigest()

def charger_status(path="pdf_status.json"):
    if os.path.exists(path):
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def sauvegarder_status(status, path="pdf_status.json"):
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(status, f, indent=2, ensure_ascii=False)

def creer_db(chemin_pdf, collection, model):
    nom_pdf = os.path.basename(chemin_pdf).replace(" ", "_").replace(".pdf", "")
    supprimer_chunks_pdf(collection, nom_pdf)
    texte = extraire_texte_depuis_pdf(chemin_pdf)
    if texte is None:
        return False
    chunks = decouper_texte(texte)
    print(f"[DEBUG] Premiers chunks extraits de {chemin_pdf} :")
    for i, chunk in enumerate(chunks[:3]):
        print(f"  Chunk {i}: {chunk[:200]}...")
    for i, chunk in enumerate(chunks):
        embedding = model.encode(chunk).tolist()
        id_chunk = f"{nom_pdf}_chunk_{i}"
        collection.upsert(
            documents=[chunk],
            embeddings=[embedding],
            ids=[id_chunk]
        )
    print("SUCCES: Nouveau contenu injecté avec succès dans la base vectorielle.")
    return True

def initialiser_base_donnees():
    """Initialise automatiquement la base de données si nécessaire"""
    print("Vérification de la base de données...")
    
    dossier_pdfs = "PDFs"
    status_path = "pdf_status.json"
    status = charger_status(status_path)
    
    if not os.path.exists(dossier_pdfs):
        print(f"ERREUR: Dossier {dossier_pdfs} introuvable. Création du dossier...")
        os.makedirs(dossier_pdfs, exist_ok=True)
        print(f"SUCCES: Dossier {dossier_pdfs} créé. Ajoutez vos PDFs dans ce dossier.")
        return
    
    pdfs = [f for f in os.listdir(dossier_pdfs) if f.lower().endswith('.pdf')]
    
    if not pdfs:
        print(f"ATTENTION: Aucun PDF trouvé dans le dossier {dossier_pdfs}.")
        print("Ajoutez vos PDFs dans ce dossier pour que le bot puisse répondre aux questions.")
        return
    
    model = SentenceTransformer("all-MiniLM-L6-v2")
    maj = False
    
    for pdf in pdfs:
        chemin_pdf = os.path.join(dossier_pdfs, pdf)
        hash_actuel = hash_pdf(chemin_pdf)
        if pdf not in status or status[pdf] != hash_actuel:
            print(f"MAJ: Mise à jour de : {pdf}")
            if creer_db(chemin_pdf, collection, model):
                status[pdf] = hash_actuel
                maj = True
        else:
            print(f"OK: Pas de modification détectée pour : {pdf}")
    
    if maj:
        sauvegarder_status(status, status_path)
        print("\nSUCCES: Fichier pdf_status.json mis à jour.")
    else:
        print("\nBase de données à jour. Prêt à répondre aux questions !")

# ==== Fonction utilitaire : détection du domaine immobilier suisse ====
# (Supprimée car plus utilisée)

# ==== Fonction pour enrichir le contexte avec les données temps réel ====
def enrichir_contexte_avec_taux_reel(question, contexte_base):
    """Enrichit le contexte avec les données de taux hypothécaires en temps réel"""
    
    # Détecter si la question concerne les taux hypothécaires
    question_lower = question.lower()
    mots_cles_taux = ["taux", "intérêt", "hypothèque", "prêt", "financement", "mensualité", "simulation"]
    
    if any(mot in question_lower for mot in mots_cles_taux):
        try:
            # Récupérer les taux actuels
            taux_info = mortgage_scraper.get_mortgage_info_summary()
            
            # Ajouter les informations de taux au contexte
            contexte_enrichi = contexte_base + "\n\n=== DONNÉES TEMPS RÉEL - TAUX HYPOTHÉCAIRES ===\n" + taux_info
            
            return contexte_enrichi
        except Exception as e:
            print(f"ERREUR: Impossible de récupérer les taux actuels: {e}")
            return contexte_base
    
    return contexte_base

# ==== Fonction pour calculer des simulations hypothécaires ====
def calculer_simulation_hypothecaire(question):
    """Calcule une simulation hypothécaire basée sur la question"""
    
    question_lower = question.lower()
    
    # Détecter les paramètres dans la question
    import re
    
    # Chercher le montant du prêt
    montant_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:chf|francs?|k|000)', question_lower)
    montant = None
    if montant_match:
        montant_str = montant_match.group(1)
        if 'k' in question_lower or '000' in question_lower:
            montant = float(montant_str) * 1000
        else:
            montant = float(montant_str)
    
    # Chercher la durée
    duree_match = re.search(r'(\d+)\s*(?:ans?|années?)', question_lower)
    duree = int(duree_match.group(1)) if duree_match else None
    
    if montant and duree:
        try:
            simulation = mortgage_scraper.calculate_mortgage_simulation(montant, duree)
            if simulation:
                return f"""
SIMULATION HYPOTHÉCAIRE:
• Montant du prêt: {simulation['loan_amount']:,.0f} CHF
• Durée: {simulation['duration_years']} ans
• Taux d'intérêt: {simulation['interest_rate']}%
• Mensualité: {simulation['monthly_payment']:,.0f} CHF
• Coût total: {simulation['total_cost']:,.0f} CHF
• Coût des intérêts: {simulation['total_cost'] - simulation['loan_amount']:,.0f} CHF
"""
        except Exception as e:
            print(f"ERREUR: Impossible de calculer la simulation: {e}")
    
    return None

def build_system_prompt(context_enrichi, model_name=None):
    base_prompt = (
        "You are a professional Swiss real estate assistant. Always answer in French. "
        "Your answers must always be clear, detailed, and well-structured, regardless of the question.\n\n"
        "Base ta réponse uniquement sur les informations extraites des documents fournis et des liens web.\n"
        "If the information is not available in these sources, say so politely to the user.\n\n"
        "Instructions for every answer:\n"
        "- Structure your answer in a professional and readable way, using Markdown (titles, lists, bold, sections, etc.) when it helps clarity.\n"
        "- Use bullet points, sections, or advice only if it is relevant to the question.\n"
        "- Add line breaks to make the answer easy to read.\n"
        "- You may use emojis to make the answer friendlier and more professional, but only if it feels natural and adds value to the response.\n"
        "- If you don't have the information, say so politely.\n"
        "- Adopte un ton professionnel, conversationnel et naturel.\n"
        "- N’utilise pas de formule de salutation en début de réponse (ex : Bonjour, Salut, etc.), sauf pour la toute première interaction.\n"
        "- Respecte la logique et la continuité de toute la conversation précédente, pour donner des réponses cohérentes et contextuelles.\n\n"
        "Adapte la structure et le niveau de détail à la question de l'utilisateur. Ne reprends pas toujours le même modèle. Sois aussi utile et informatif que possible.\n\n"
        "Voici les informations dont tu disposes :\n"
        f"{context_enrichi}\n"
    )
    if model_name and "mistral" in model_name.lower():
        base_prompt = (
            "IMPORTANT : NE RÉPONDS JAMAIS à une question qui ne concerne pas l’immobilier en Suisse. "
            "Si la question sort de ce domaine, réponds uniquement : 'Je ne peux répondre qu’aux questions sur l’immobilier en Suisse.'\n\n"
        ) + base_prompt
    return base_prompt

# ==== Fonction principale ====
def ask_bot(question, messages=None, model_name=None):
    if len(question.split()) > MAX_QUESTION_TOKENS:
        return "ATTENTION: Votre question est trop longue. Veuillez la reformuler plus simplement."

    simulation_info = calculer_simulation_hypothecaire(question)
    docs = collection.query(query_texts=[question], n_results=5)
    if not docs["documents"] or not docs["documents"][0]:
        if simulation_info:
            return simulation_info
        return "Désolé, je ne trouve pas l'information dans ma base documentaire pour répondre à cette question."
    context = "\n---\n".join(docs["documents"][0])
    context_enrichi = enrichir_contexte_avec_taux_reel(question, context)
    if simulation_info:
        context_enrichi += "\n\n" + simulation_info

    system_prompt = build_system_prompt(context_enrichi, model_name)

    if messages:
        llm_messages = []
        if not messages or messages[0].get("role") != "system":
            llm_messages.append({"role": "system", "content": system_prompt})
        for msg in messages:
            if msg.get("role") in ("user", "assistant") and msg.get("content"):
                llm_messages.append({"role": msg["role"], "content": msg["content"]})
        if not (llm_messages and llm_messages[-1]["role"] == "user" and llm_messages[-1]["content"] == question):
            llm_messages.append({"role": "user", "content": question})
        return ask_llm(llm_messages, model_name=model_name)
    else:
        prompt = f"{system_prompt}\n\nQuestion utilisateur : {question}"
        return ask_llm(prompt, model_name=model_name)

def ask_bot_stream(question, messages=None, model_name=None):
    if len(question.split()) > MAX_QUESTION_TOKENS:
        yield "ATTENTION: Votre question est trop longue. Veuillez la reformuler plus simplement."
        return

    simulation_info = calculer_simulation_hypothecaire(question)
    docs = collection.query(query_texts=[question], n_results=5)
    if not docs["documents"] or not docs["documents"][0]:
        if simulation_info:
            yield simulation_info
            return
        yield "Désolé, je ne trouve pas l'information dans ma base documentaire pour répondre à cette question."
        return
    context = "\n---\n".join(docs["documents"][0])
    context_enrichi = enrichir_contexte_avec_taux_reel(question, context)
    if simulation_info:
        context_enrichi += "\n\n" + simulation_info

    system_prompt = build_system_prompt(context_enrichi, model_name)

    if messages:
        llm_messages = []
        if not messages or messages[0].get("role") != "system":
            llm_messages.append({"role": "system", "content": system_prompt})
        for msg in messages:
            if msg.get("role") in ("user", "assistant") and msg.get("content"):
                llm_messages.append({"role": msg["role"], "content": msg["content"]})
        if not (llm_messages and llm_messages[-1]["role"] == "user" and llm_messages[-1]["content"] == question):
            llm_messages.append({"role": "user", "content": question})
        for token in ask_llm_stream(llm_messages, model_name=model_name):
            yield token
    else:
        prompt = f"{system_prompt}\n\nQuestion utilisateur : {question}"
        for token in ask_llm_stream(prompt, model_name=model_name):
            yield token

# ==== Interface terminal ====
if __name__ == "__main__":
    # Initialiser automatiquement la base de données au démarrage
    initialiser_base_donnees()
    
    print("\nASSISTANT IMMOBILIER SUISSE - PRET !")
    print("Posez vos questions sur l'immobilier en Suisse (achat, vente, investissement, fiscalité, etc.)")
    print("Tapez 'exit' pour quitter.\n")
    
    if len(sys.argv) > 1:
        # Mode ligne de commande
        user_question = " ".join(sys.argv[1:])
        ask_bot(user_question)
    else:
        # Mode interactif
        while True:
            try:
                user_question = input("\nPose ta question (ou tape 'exit' pour quitter) : ")
            except EOFError:
                print("\nFin de l'entrée utilisateur. Arrêt du programme.")
                break
            if user_question.lower() in ["exit", "quit", "q"]:
                break
            ask_bot(user_question)
