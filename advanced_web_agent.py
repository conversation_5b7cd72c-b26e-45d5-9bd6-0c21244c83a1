#!/usr/bin/env python3
"""
Agent IA avancé pour le scraping intelligent des données hypothécaires suisses
Utilise Selenium pour interagir avec les formulaires et récupérer des données en temps réel
"""

import time
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import sqlite3
import requests
from bs4 import BeautifulSoup
import re

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedMortgageAgent:
    def __init__(self, db_path="mortgage_data.db", headless=True):
        """Initialise l'agent IA avancé"""
        self.db_path = db_path
        self.headless = headless
        self.driver = None
        self.init_database()
        self.init_webdriver()
        
    def init_database(self):
        """Initialise la base de données avec des tables avancées"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table pour les taux hypothécaires (améliorée)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mortgage_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                duration TEXT NOT NULL,
                rate REAL NOT NULL,
                bank TEXT,
                source_url TEXT,
                confidence_score REAL DEFAULT 1.0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table pour les simulations de calculateur (améliorée)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mortgage_simulations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                loan_amount REAL NOT NULL,
                duration_years INTEGER NOT NULL,
                monthly_payment REAL,
                total_cost REAL,
                interest_rate REAL,
                source_url TEXT,
                calculation_method TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table pour les tentatives de scraping
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scraping_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT NOT NULL,
                success BOOLEAN,
                error_message TEXT,
                response_time REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Base de données avancée initialisée")
    
    def init_webdriver(self):
        """Initialise le navigateur Selenium avec configuration anti-détection"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # Configuration anti-détection
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # User agent réaliste
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Désactiver les images pour accélérer
            prefs = {"profile.managed_default_content_settings.images": 2}
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Masquer que c'est un bot
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Navigateur Selenium initialisé avec succès")
            
        except Exception as e:
            logger.error(f"Erreur lors de l'initialisation du navigateur: {e}")
            raise extract_moneypark_rates(self) extract_moneypark_rates(self)
    
    def scrape_moneypark_rates(self):
        """Scrape intelligent des taux depuis MoneyPark avec gestion des anti-bot"""
        url = "https://moneypark.ch/fr/hypotheque/taux-interet-hypothecaires-actuels/"
        
        try:
            logger.info(f"Tentative de scraping intelligent depuis: {url}")
            start_time = time.time()
            
            # Naviguer vers la page
            self.driver.get(url)
            
            # Attendre que la page se charge
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Sauvegarder le HTML rendu pour debug
            with open("selenium_moneypark_dump.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info("HTML de la page MoneyPark sauvegardé dans selenium_moneypark_dump.html")
            
            # Simuler un comportement humain
            self.simulate_human_behavior()
            
            # Extraire les taux avec différentes stratégies
            rates_data = []
            
            # Stratégie 1: Chercher des tableaux de taux
            rates_data.extend(self.extract_rates_from_tables())
            
            # Stratégie 2: Chercher des éléments avec des pourcentages
            rates_data.extend(self.extract_rates_from_elements())
            
            # Stratégie 3: Analyser le texte de la page
            rates_data.extend(self.extract_rates_from_text())
            
            # Dédupliquer et valider
            rates_data = self.deduplicate_and_validate_rates(rates_data)
            
            response_time = time.time() - start_time
            
            # Enregistrer la tentative
            self.log_scraping_attempt(url, True, None, response_time)
            
            if rates_data:
                self.save_rates_to_db(rates_data, url)
                logger.info(f"Scraping réussi: {len(rates_data)} taux récupérés en {response_time:.2f}s")
                return rates_data
            else:
                logger.warning("Aucun taux trouvé avec les stratégies avancées")
                return self.get_fallback_rates()
                
        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            self.log_scraping_attempt(url, False, str(e), response_time)
            logger.error(f"Erreur lors du scraping intelligent: {e}")
            return self.get_fallback_rates()
    
    def simulate_human_behavior(self):
        """Simule un comportement humain pour éviter la détection"""
        try:
            # Scroll progressif
            for i in range(3):
                self.driver.execute_script(f"window.scrollTo(0, {i * 300});")
                time.sleep(0.5)
            
            # Retour en haut
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
            # Mouvement de souris simulé (si pas en headless)
            if not self.headless:
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.driver)
                actions.move_by_offset(100, 100).perform()
            
            logger.info("Comportement humain simulé")
            
        except Exception as e:
            logger.warning(f"Erreur lors de la simulation comportementale: {e}")
    
    def extract_rates_from_tables(self):
        """Extrait les taux depuis les tableaux HTML"""
        rates = []
        try:
            # Chercher des tableaux
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            
            for table in tables:
                rows = table.find_elements(By.TAG_NAME, "tr")
                
                for row in rows:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) >= 2:
                        text = row.text.lower()
                        
                        # Patterns pour détecter les taux dans les tableaux
                        patterns = [
                            r'(\d+)\s*ans?\s*[:\-]?\s*(\d+[,.]?\d*)%',
                            r'(\d+[,.]?\d*)%\s*(?:pour\s*)?(\d+)\s*ans?'
                        ]
                        
                        for pattern in patterns:
                            matches = re.finditer(pattern, text)
                            for match in matches:
                                if len(match.groups()) == 2:
                                    try:
                                        rate = float(match.group(1).replace(',', '.'))
                                        duration = int(match.group(2))
                                        
                                        if 0.5 <= rate <= 10 and 1 <= duration <= 50:
                                            rates.append({
                                                'duration': f"{duration} ans",
                                                'rate': rate,
                                                'bank': 'MoneyPark',
                                                'source_url': self.driver.current_url,
                                                'confidence_score': 0.9,
                                                'timestamp': datetime.now()
                                            })
                                    except ValueError:
                                        continue
            
            logger.info(f"Taux extraits des tableaux: {len(rates)}")
            return rates
            
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des tableaux: {e}")
            return []
    
    def extract_rates_from_elements(self):
        """Extrait les taux depuis des éléments HTML spécifiques"""
        rates = []
        try:
            # Chercher des éléments avec des classes spécifiques
            selectors = [
                ".rate", ".interest-rate", ".mortgage-rate",
                "[class*='rate']", "[class*='interest']", "[class*='mortgage']"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        text = element.text
                        if '%' in text and any(str(i) in text for i in range(1, 51)):
                            # Analyser le texte de l'élément
                            rates.extend(self.parse_rate_text(text))
                            
                except Exception:
                    continue
            
            logger.info(f"Taux extraits des éléments: {len(rates)}")
            return rates
            
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des éléments: {e}")
            return []
    
    def extract_rates_from_text(self):
        """Extrait les taux depuis le texte de la page"""
        rates = []
        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # Patterns avancés pour détecter les taux
            patterns = [
                r'(\d+[,.]?\d*)%\s*(?:pour\s*)?(\d+)\s*ans?',
                r'(\d+)\s*ans?\s*[:\-]?\s*(\d+[,.]?\d*)%',
                r'taux\s*(?:de\s*)?(\d+[,.]?\d*)%\s*(?:sur\s*)?(\d+)\s*ans?',
                r'hypothèque\s*(?:à\s*)?(\d+[,.]?\d*)%\s*(?:sur\s*)?(\d+)\s*ans?'
            ]
            
            for pattern in patterns:
                matches = re.finditer(pattern, page_text, re.IGNORECASE)
                for match in matches:
                    if len(match.groups()) == 2:
                        try:
                            rate = float(match.group(1).replace(',', '.'))
                            duration = int(match.group(2))
                            
                            if 0.5 <= rate <= 10 and 1 <= duration <= 50:
                                rates.append({
                                    'duration': f"{duration} ans",
                                    'rate': rate,
                                    'bank': 'MoneyPark',
                                    'source_url': self.driver.current_url,
                                    'confidence_score': 0.7,
                                    'timestamp': datetime.now()
                                })
                        except ValueError:
                            continue
            
            logger.info(f"Taux extraits du texte: {len(rates)}")
            return rates
            
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction du texte: {e}")
            return []
    
    def parse_rate_text(self, text):
        """Parse le texte pour extraire les taux"""
        rates = []
        patterns = [
            r'(\d+[,.]?\d*)%\s*(?:pour\s*)?(\d+)\s*ans?',
            r'(\d+)\s*ans?\s*[:\-]?\s*(\d+[,.]?\d*)%'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) == 2:
                    try:
                        rate = float(match.group(1).replace(',', '.'))
                        duration = int(match.group(2))
                        
                        if 0.5 <= rate <= 10 and 1 <= duration <= 50:
                            rates.append({
                                'duration': f"{duration} ans",
                                'rate': rate,
                                'bank': 'MoneyPark',
                                'source_url': self.driver.current_url,
                                'confidence_score': 0.8,
                                'timestamp': datetime.now()
                            })
                    except ValueError:
                        continue
        
        return rates
    
    def deduplicate_and_validate_rates(self, rates):
        """Déduplique et valide les taux extraits"""
        unique_rates = {}
        
        for rate in rates:
            key = f"{rate['duration']}_{rate['bank']}"
            
            if key not in unique_rates:
                unique_rates[key] = rate
            else:
                # Garder le taux avec le score de confiance le plus élevé
                if rate['confidence_score'] > unique_rates[key]['confidence_score']:
                    unique_rates[key] = rate
        
        # Trier par durée
        sorted_rates = sorted(unique_rates.values(), key=lambda x: int(x['duration'].split()[0]))
        
        logger.info(f"Taux dédupliqués et validés: {len(sorted_rates)}")
        return sorted_rates
    
    def use_moneypark_calculator(self, loan_amount, duration_years, property_value=None):
        """Utilise le calculateur MoneyPark pour obtenir une simulation réelle"""
        url = "https://moneypark.ch/fr/hypotheque/calculateur/"
        
        try:
            logger.info(f"Utilisation du calculateur MoneyPark pour {loan_amount} CHF sur {duration_years} ans")
            
            # Naviguer vers le calculateur
            self.driver.get(url)
            
            # Attendre que la page se charge
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Simuler un comportement humain
            self.simulate_human_behavior()
            
            # Remplir le formulaire
            simulation_data = self.fill_calculator_form(loan_amount, duration_years, property_value)
            
            if simulation_data:
                self.save_simulation_to_db(simulation_data, url, "MoneyPark Calculator")
                logger.info("Simulation MoneyPark réussie")
                return simulation_data
            else:
                logger.warning("Impossible d'obtenir la simulation MoneyPark")
                return self.calculate_local_simulation(loan_amount, duration_years)
                
        except Exception as e:
            logger.error(f"Erreur lors de l'utilisation du calculateur: {e}")
            return self.calculate_local_simulation(loan_amount, duration_years)
    
    def fill_calculator_form(self, loan_amount, duration_years, property_value):
        """Remplit le formulaire du calculateur MoneyPark"""
        try:
            # Attendre que les champs soient disponibles
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "input"))
            )
            
            # Chercher et remplir les champs
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            
            for input_field in inputs:
                try:
                    # Identifier le type de champ
                    field_type = self.identify_input_field(input_field)
                    
                    if field_type == "loan_amount":
                        self.fill_input_field(input_field, str(loan_amount))
                    elif field_type == "duration" and duration_years:
                        self.fill_input_field(input_field, str(duration_years))
                    elif field_type == "property_value" and property_value:
                        self.fill_input_field(input_field, str(property_value))
                        
                except Exception as e:
                    logger.debug(f"Erreur lors du remplissage d'un champ: {e}")
                    continue
            
            # Chercher et cliquer sur le bouton de calcul
            self.click_calculate_button()
            
            # Attendre les résultats
            time.sleep(3)
            
            # Extraire les résultats
            return self.extract_calculator_results()
            
        except Exception as e:
            logger.error(f"Erreur lors du remplissage du formulaire: {e}")
            return None
    
    def identify_input_field(self, input_element):
        """Identifie le type de champ d'entrée"""
        try:
            # Vérifier les attributs
            placeholder = input_element.get_attribute("placeholder") or ""
            name = input_element.get_attribute("name") or ""
            id_attr = input_element.get_attribute("id") or ""
            label = self.find_label_for_input(input_element)
            
            text_to_analyze = f"{placeholder} {name} {id_attr} {label}".lower()
            
            # Patterns pour identifier les champs
            if any(word in text_to_analyze for word in ["montant", "prêt", "emprunt", "loan", "amount"]):
                return "loan_amount"
            elif any(word in text_to_analyze for word in ["durée", "années", "years", "duration"]):
                return "duration"
            elif any(word in text_to_analyze for word in ["valeur", "prix", "property", "value"]):
                return "property_value"
            
            return "unknown"
            
        except Exception:
            return "unknown"
    
    def find_label_for_input(self, input_element):
        """Trouve le label associé à un champ d'entrée"""
        try:
            # Chercher le label par for
            input_id = input_element.get_attribute("id")
            if input_id:
                label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{input_id}']")
                return label.text
        except:
            pass
        
        try:
            # Chercher le label parent
            parent = input_element.find_element(By.XPATH, "./..")
            labels = parent.find_elements(By.TAG_NAME, "label")
            if labels:
                return labels[0].text
        except:
            pass
        
        return ""
    
    def fill_input_field(self, input_element, value):
        """Remplit un champ d'entrée"""
        try:
            # Vider le champ
            input_element.clear()
            time.sleep(0.2)
            
            # Remplir progressivement
            for char in value:
                input_element.send_keys(char)
                time.sleep(0.05)
            
            # Déclencher l'événement change
            input_element.send_keys(Keys.TAB)
            time.sleep(0.5)
            
            logger.debug(f"Champ rempli avec: {value}")
            
        except Exception as e:
            logger.error(f"Erreur lors du remplissage du champ: {e}")
    
    def click_calculate_button(self):
        """Clique sur le bouton de calcul"""
        try:
            # Chercher différents types de boutons
            button_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Calculer')",
                "button:contains('Calculate')",
                "button:contains('Simuler')",
                ".calculate-button",
                ".submit-button"
            ]
            
            for selector in button_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            button.click()
                            logger.info("Bouton de calcul cliqué")
                            return
                except:
                    continue
            
            # Si aucun bouton trouvé, essayer de presser Enter
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.RETURN)
            logger.info("Tentative avec touche Enter")
            
        except Exception as e:
            logger.error(f"Erreur lors du clic sur le bouton: {e}")
    
    def extract_calculator_results(self):
        """Extrait les résultats du calculateur"""
        try:
            # Chercher les résultats dans différents formats
            result_selectors = [
                ".result", ".calculation-result", ".mortgage-result",
                "[class*='result']", "[class*='calculation']", "[class*='payment']"
            ]
            
            for selector in result_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        text = element.text
                        if any(word in text.lower() for word in ["chf", "franc", "mensualité", "payment"]):
                            return self.parse_calculator_results(text)
                            
                except:
                    continue
            
            # Si pas de résultats structurés, analyser toute la page
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
            return self.parse_calculator_results(page_text)
            
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des résultats: {e}")
            return None
    
    def parse_calculator_results(self, text):
        """Parse les résultats du calculateur"""
        try:
            # Patterns pour extraire les montants
            monthly_pattern = r'(?:mensualité|payment|monthly)\s*:?\s*([\d\s,]+)\s*(?:chf|franc)'
            total_pattern = r'(?:total|coût total|total cost)\s*:?\s*([\d\s,]+)\s*(?:chf|franc)'
            rate_pattern = r'(\d+[,.]?\d*)%'
            
            monthly_match = re.search(monthly_pattern, text, re.IGNORECASE)
            total_match = re.search(total_pattern, text, re.IGNORECASE)
            rate_match = re.search(rate_pattern, text)
            
            if monthly_match:
                monthly_payment = float(monthly_match.group(1).replace(' ', '').replace(',', ''))
                
                # Estimer les autres valeurs si pas trouvées
                total_cost = None
                if total_match:
                    total_cost = float(total_match.group(1).replace(' ', '').replace(',', ''))
                
                interest_rate = None
                if rate_match:
                    interest_rate = float(rate_match.group(1).replace(',', '.'))
                
                return {
                    'monthly_payment': monthly_payment,
                    'total_cost': total_cost,
                    'interest_rate': interest_rate,
                    'calculation_method': 'MoneyPark Calculator',
                    'timestamp': datetime.now()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Erreur lors du parsing des résultats: {e}")
            return None
    
    def calculate_local_simulation(self, loan_amount, duration_years, interest_rate=None):
        """Calcul local de simulation (fallback)"""
        try:
            if interest_rate is None:
                latest_rates = self.get_latest_rates()
                for rate in latest_rates:
                    if str(duration_years) in rate['duration']:
                        interest_rate = rate['rate']
                        break
                else:
                    interest_rate = 3.5
            
            monthly_rate = interest_rate / 100 / 12
            total_months = duration_years * 12
            
            if monthly_rate > 0:
                monthly_payment = loan_amount * (monthly_rate * (1 + monthly_rate)**total_months) / ((1 + monthly_rate)**total_months - 1)
            else:
                monthly_payment = loan_amount / total_months
            
            total_cost = monthly_payment * total_months
            
            return {
                'loan_amount': loan_amount,
                'duration_years': duration_years,
                'monthly_payment': monthly_payment,
                'total_cost': total_cost,
                'interest_rate': interest_rate,
                'calculation_method': 'Local Calculation',
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Erreur lors du calcul local: {e}")
            return None
    
    def save_rates_to_db(self, rates_data, source_url):
        """Sauvegarde les taux en base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for rate in rates_data:
            cursor.execute('''
                INSERT INTO mortgage_rates (duration, rate, bank, source_url, confidence_score, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (rate['duration'], rate['rate'], rate['bank'], source_url, rate['confidence_score'], rate['timestamp']))
        
        conn.commit()
        conn.close()
        logger.info("Taux sauvegardés en base de données")
    
    def save_simulation_to_db(self, simulation_data, source_url, calculation_method):
        """Sauvegarde une simulation en base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO mortgage_simulations 
            (loan_amount, duration_years, monthly_payment, total_cost, interest_rate, source_url, calculation_method, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            simulation_data.get('loan_amount', 0),
            simulation_data.get('duration_years', 0),
            simulation_data.get('monthly_payment', 0),
            simulation_data.get('total_cost', 0),
            simulation_data.get('interest_rate', 0),
            source_url,
            calculation_method,
            simulation_data['timestamp']
        ))
        
        conn.commit()
        conn.close()
    
    def log_scraping_attempt(self, url, success, error_message, response_time):
        """Enregistre une tentative de scraping"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO scraping_attempts (url, success, error_message, response_time, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', (url, success, error_message, response_time, datetime.now()))
        
        conn.commit()
        conn.close()
    
    def get_latest_rates(self):
        """Récupère les derniers taux depuis la base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT duration, rate, bank, source_url, confidence_score, timestamp 
            FROM mortgage_rates 
            WHERE timestamp = (
                SELECT MAX(timestamp) FROM mortgage_rates
            )
            ORDER BY duration
        ''')
        
        rates = cursor.fetchall()
        conn.close()
        
        return [{'duration': r[0], 'rate': r[1], 'bank': r[2], 'source_url': r[3], 'confidence_score': r[4], 'timestamp': r[5]} for r in rates]
    
    def get_fallback_rates(self):
        """Retourne des taux par défaut en cas d'échec"""
        default_rates = [
            {'duration': '5 ans', 'rate': 3.2, 'bank': 'MoneyPark', 'source_url': 'fallback', 'confidence_score': 0.1, 'timestamp': datetime.now()},
            {'duration': '10 ans', 'rate': 3.5, 'bank': 'MoneyPark', 'source_url': 'fallback', 'confidence_score': 0.1, 'timestamp': datetime.now()},
            {'duration': '15 ans', 'rate': 3.8, 'bank': 'MoneyPark', 'source_url': 'fallback', 'confidence_score': 0.1, 'timestamp': datetime.now()},
            {'duration': '20 ans', 'rate': 4.0, 'bank': 'MoneyPark', 'source_url': 'fallback', 'confidence_score': 0.1, 'timestamp': datetime.now()}
        ]
        
        self.save_rates_to_db(default_rates, 'fallback')
        return default_rates
    
    def get_scraping_statistics(self):
        """Retourne les statistiques de scraping"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Statistiques des tentatives
        cursor.execute('''
            SELECT 
                COUNT(*) as total_attempts,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_attempts,
                AVG(response_time) as avg_response_time
            FROM scraping_attempts
        ''')
        
        stats = cursor.fetchone()
        
        # Dernières tentatives
        cursor.execute('''
            SELECT url, success, error_message, response_time, timestamp
            FROM scraping_attempts
            ORDER BY timestamp DESC
            LIMIT 5
        ''')
        
        recent_attempts = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_attempts': stats[0],
            'successful_attempts': stats[1],
            'success_rate': (stats[1] / stats[0] * 100) if stats[0] > 0 else 0,
            'avg_response_time': stats[2],
            'recent_attempts': recent_attempts
        }
    
    def close(self):
        """Ferme le navigateur"""
        if self.driver:
            self.driver.quit()
            logger.info("Navigateur fermé")

# Test de l'agent avancé
if __name__ == "__main__":
    agent = AdvancedMortgageAgent(headless=False)  # False pour voir ce qui se passe
    
    try:
        print("=== TEST DE L'AGENT IA AVANCÉ ===")
        
        # Test 1: Scraping intelligent des taux
        print("\n1. Test scraping intelligent des taux...")
        rates = agent.scrape_moneypark_rates()
        
        if rates:
            print(f"✓ {len(rates)} taux récupérés:")
            for rate in rates:
                print(f"  - {rate['duration']}: {rate['rate']}% (confiance: {rate['confidence_score']})")
        
        # Test 2: Utilisation du calculateur
        print("\n2. Test utilisation du calculateur...")
        simulation = agent.use_moneypark_calculator(500000, 20)
        
        if simulation:
            print("✓ Simulation réussie:")
            print(f"  - Mensualité: {simulation.get('monthly_payment', 'N/A'):,.0f} CHF")
            print(f"  - Méthode: {simulation.get('calculation_method', 'N/A')}")
        
        # Test 3: Statistiques
        print("\n3. Statistiques de scraping...")
        stats = agent.get_scraping_statistics()
        print(f"✓ Taux de succès: {stats['success_rate']:.1f}%")
        print(f"✓ Temps de réponse moyen: {stats['avg_response_time']:.2f}s")
        
    finally:
        agent.close() 